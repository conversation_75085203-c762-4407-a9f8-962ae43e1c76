/* تنسيق صفحة المكافآت والخصومات */
@import url('shared-styles.css');
.rewards-deductions-page {
  background-color: #f5f5f5;
}

/* تم نقل تنسيقات التبويبات إلى ملف shared-styles.css */

/* تنسيق جداول المكافآت والخصومات - تم نقل التنسيقات المشتركة إلى ملف shared-styles.css */
.rewards-table-container,
.deductions-table-container {
  margin-top: 30px;
  overflow-x: auto;
}

/* تنسيق الصف المميز (آخر إضافة) للمكافآت */
.rewards-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.rewards-table tr:first-child td {
  font-weight: bold;
}

/* تنسيق الصف المميز (آخر إضافة) للخصومات */
.deductions-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.deductions-table tr:first-child td {
  font-weight: bold;
}

/* تنسيقات النماذج - تم نقل التنسيقات المشتركة إلى ملف shared-styles.css */
.rewards-form,
.deductions-form,
.reward-form,
.deduction-form {
  display: block;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* تنسيق صفوف النموذج */
.reward-form .form-row,
.deduction-form .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

/* تنسيق الحقول المفردة */
.reward-form .form-group:not(.form-row .form-group),
.deduction-form .form-group:not(.form-row .form-group) {
  margin-bottom: 15px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

/* تم نقل تنسيقات الأزرار إلى ملف shared-styles.css */

/* تنسيق فلاتر البحث */
.search-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border: 1px solid #e0e0e0;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.filter-actions {
  display: flex;
  gap: 10px;
  align-items: end;
  flex-wrap: wrap;
}

/* تنسيق الجداول */
.table-container {
  overflow-x: auto;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* تم نقل تنسيقات .data-table إلى shared-styles.css */

.data-table th {
  background-color: var(--primary-color);
  color: white;
  padding: 12px 8px;
  text-align: center;
  font-weight: bold;
  border: 1px solid var(--primary-dark);
}

.data-table td {
  padding: 10px 8px;
  text-align: center;
  border: 1px solid #ddd;
  vertical-align: middle;
}

.data-table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.data-table tbody tr:hover {
  background-color: #e3f2fd;
}

/* تنسيق المودال */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: #fefefe;
  margin: 5% auto;
  padding: 0;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);

}

/* تم إزالة تأثير modalSlideIn لتحسين الأداء */

.modal-header {
  background-color: #2196F3;
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
}

.close {
  color: white;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;

}

.close:hover {
  color: #ffcdd2;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn {
  background-color: #9e9e9e;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;

}

.cancel-btn:hover {
  background-color: #757575;
}

/* تنسيق اقتراحات البحث */
.employee-search-input {
  position: relative;
}

/* تنسيق responsive */
@media (max-width: 1200px) {
  .reward-form .form-row,
  .deduction-form .form-row {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

@media (max-width: 900px) {
  .reward-form .form-row,
  .deduction-form .form-row {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .reward-form .form-row,
  .deduction-form .form-row {
    grid-template-columns: 1fr;
  }
  
  .search-filters {
    grid-template-columns: 1fr;
  }
  
  .filter-actions {
    justify-content: center;
  }
  
  .tabs {
    justify-content: center;
  }
  
  .tab-btn {
    flex: 1;
    min-width: 120px;
    text-align: center;
  }
  
  .data-table {
    font-size: 12px;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 95%;
    margin: 10% auto;
  }
  
  .tab-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  /* تم نقل تنسيقات النماذج إلى shared-styles.css */
}

/* تنسيق حالات التحميل */
.loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.loading::after {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #2196F3;
  border-radius: 50%;

  margin-right: 10px;
}

/* تم إزالة تأثير spin لتحسين الأداء */

/* تنسيق رسائل النجاح والخطأ */
.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px 15px;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
  margin-bottom: 15px;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin-bottom: 15px;
}

/* تنسيق الأرقام */
.amount-cell {
  font-weight: bold;
  color: #2196F3;
}

.reward-amount {
  color: #4CAF50;
}

.deduction-amount {
  color: #f44336;
}

/* تنسيق التواريخ */
.date-cell {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  white-space: nowrap;
  cursor: help;
  position: relative;
}

.date-cell:hover {
  background-color: #e8f5e8;
}

/* تنسيق tooltip للتاريخ */
.date-cell[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 5px;
}

.date-cell[title]:hover::before {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #333;
  z-index: 1000;
}

/* تنسيق التاريخ الهجري */
.hijri-date {
  color: #2e7d32;
  font-weight: 500;
}

.gregorian-date {
  color: #1565c0;
  font-size: 11px;
  opacity: 0.8;
}

/* تنسيق التقارير */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;

}

/* تم إزالة تأثير hover لتحسين الأداء */

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.rewards-card .stat-icon {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.deductions-card .stat-icon {
  background: linear-gradient(135deg, #f44336, #da190b);
}

.net-card .stat-icon {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.net-card.positive .stat-icon {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.net-card.negative .stat-icon {
  background: linear-gradient(135deg, #f44336, #da190b);
}

.net-card.zero .stat-icon {
  background: linear-gradient(135deg, #9e9e9e, #757575);
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.rewards-card .stat-value {
  color: #4CAF50;
}

.deductions-card .stat-value {
  color: #f44336;
}

.net-card .stat-value {
  color: #2196F3;
}

.net-card.positive .stat-value {
  color: #4CAF50;
}

.net-card.negative .stat-value {
  color: #f44336;
}

.net-card.zero .stat-value {
  color: #9e9e9e;
}

.stat-count,
.stat-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.details-btn {
  background: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.details-btn:hover {
  background: #1976D2;
}

/* تنسيق فلاتر التقارير */
.reports-filters {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 30px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  align-items: end;
}

/* تنسيق الجداول القابلة للطي */
.collapsible-tables {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.table-section {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.table-header {
  background: var(--primary-color);
  color: white;
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.table-header:hover {
  background: var(--primary-dark);
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-icon {

}

/* تم إزالة تأثير rotate لتحسين الأداء */

.table-content {
  padding: 20px;
}

.table-actions {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
}

/* تنسيق Modal التفاصيل */
.large-modal {
  width: 95%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
}

.details-summary {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.summary-value {
  font-size: 20px;
  font-weight: bold;
  color: #2196F3;
}

.details-actions {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
}

/* تنسيق responsive للتقارير */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 24px;
  }

  .reports-filters {
    grid-template-columns: 1fr;
  }

  .table-header h3 {
    font-size: 14px;
  }

  .details-summary {
    flex-direction: column;
    gap: 15px;
  }

  .large-modal {
    width: 98%;
    margin: 5% auto;
  }
}

@media (max-width: 480px) {
  .stat-card {
    padding: 15px;
  }

  .stat-value {
    font-size: 20px;
  }

  .details-btn {
    padding: 6px 12px;
    font-size: 11px;
  }

  .table-header {
    padding: 10px 15px;
  }

  .table-content {
    padding: 15px;
  }
}

/* تنسيق الأقسام المدمجة */
.view-section {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 2px solid #e0e0e0;
}

.view-section h2 {
  color: #2196F3;
  margin-bottom: 20px;
  font-size: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.view-section h2::before {
  content: "📊";
  font-size: 24px;
}

/* تحسين تنسيق النماذج والجداول */
.reward-form,
.deduction-form {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 30px;
}

.search-filters {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

/* تحسين responsive للأقسام المدمجة */
@media (max-width: 768px) {
  .view-section {
    margin-top: 20px;
    padding-top: 20px;
  }

  .view-section h2 {
    font-size: 18px;
  }

  .reward-form,
  .deduction-form {
    padding: 15px;
  }

  .search-filters {
    padding: 15px;
  }
}

/* تنسيق فلاتر البحث المحددة */
.filtered-search-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.search-filters-row .form-group {
  display: flex;
  flex-direction: column;
}

.search-filters-row .form-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

.filter-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
}

.filter-actions .reset-btn,
.filter-actions .export-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.filter-actions .reset-btn {
  background-color: #6c757d;
  color: white;
}

.filter-actions .reset-btn:hover {
  background-color: #5a6268;
}

.filter-actions .export-btn {
  background-color: #28a745;
  color: white;
}

.filter-actions .export-btn:hover {
  background-color: #218838;
}