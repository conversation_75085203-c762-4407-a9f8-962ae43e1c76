const express = require('express');
const router = express.Router();
const { logAction, createEditMessage } = require('../activityLogger');
const { authenticateToken } = require('../middleware/auth');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

// دالة لإنشاء جدول الساعات الإضافية
const createExtraHoursTable = async (pool) => {
  const sql = `CREATE TABLE IF NOT EXISTS extra_hours (
    id int NOT NULL AUTO_INCREMENT,
    employee_code varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'كود الموظف',
    employee_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الموظف',
    department varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'الإدارة',
    extra_hours decimal(5,2) NOT NULL COMMENT 'عدد الساعات الإضافية',
    extra_date date NOT NULL COMMENT 'تاريخ الإضافي',
    notes text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'ملاحظات',
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    PRIMARY KEY (id),
    KEY idx_employee_code (employee_code),
    KEY idx_department (department),
    KEY idx_extra_date (extra_date),
    KEY idx_extra_hours (extra_hours)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الساعات الإضافية'`;
  
  await pool.promise().query(sql);
};

// إنشاء جدول الساعات الإضافية
router.get('/create-table', async (req, res) => {
  try {
    await createExtraHoursTable(req.app.locals.pool);
    res.json({ message: 'تم التحقق من جدول الساعات الإضافية' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول الساعات الإضافية' });
  }
});

// الحصول على جميع الساعات الإضافية
router.get('/', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createExtraHoursTable(pool);
    
    const [rows] = await pool.promise().query(`
      SELECT 
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted,
        CASE
          WHEN e.full_name IS NOT NULL THEN e.full_name
          ELSE eh.employee_name
        END as current_employee_name,
        CASE
          WHEN e.department IS NOT NULL THEN e.department
          ELSE eh.department
        END as current_department
      FROM extra_hours eh
      LEFT JOIN employees e ON eh.employee_code = e.code
      ORDER BY eh.created_at DESC, eh.id DESC
    `);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات الساعات الإضافية' });
  }
});

// إضافة ساعات إضافية جديدة
router.post('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createExtraHoursTable(pool);
    
    const { 
      employee_code, 
      extra_hours, 
      extra_date, 
      notes 
    } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !extra_hours || !extra_date) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب تعبئتها' });
    }

    // التحقق من صحة عدد الساعات
    if (parseFloat(extra_hours) <= 0) {
      return res.status(400).json({ error: 'عدد الساعات الإضافية يجب أن يكون أكبر من صفر' });
    }

    // الحصول على بيانات الموظف
    const [employeeResult] = await pool.promise().query(
      'SELECT full_name, department, status FROM employees WHERE code = ?',
      [employee_code]
    );

    if (employeeResult.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    // التحقق من حالة الموظف
    if (employeeResult[0].status === 'مستقيل') {
      return res.status(400).json({ error: 'لا يمكن إضافة ساعات إضافية لموظف مستقيل' });
    }

    // فحص عدم تكرار نفس الموظف في نفس التاريخ
    const [existingRecord] = await pool.promise().query(
      'SELECT id FROM extra_hours WHERE employee_code = ? AND extra_date = ?',
      [employee_code, extra_date]
    );

    if (existingRecord.length > 0) {
      return res.status(409).json({
        error: 'تم تسجيل عمل إضافي لهذا الموظف في نفس التاريخ مسبقاً',
        duplicate: true,
        existing_date: extra_date
      });
    }

    const employeeName = employeeResult[0].full_name;
    const department = employeeResult[0].department;

    const sql = `
      INSERT INTO extra_hours (
        employee_code, employee_name, department, extra_hours, 
        extra_date, notes
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const values = [
      employee_code,
      employeeName,
      department,
      parseFloat(extra_hours),
      extra_date,
      notes || null
    ];
    
    const [result] = await pool.promise().query(sql, values);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'extra_hours',
      record_id: result.insertId.toString(),
      message: `تم إضافة ساعات إضافية للموظف: ${employeeName} (كود: ${employee_code}) - عدد الساعات: ${extra_hours} ساعة - التاريخ: ${extra_date}`
    });

    res.status(201).json({
      message: 'تم إضافة الساعات الإضافية بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في إضافة الساعات الإضافية' });
  }
});

// تحديث ساعات إضافية
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const extraHourId = req.params.id;

    const {
      employee_code,
      extra_hours,
      extra_date,
      notes
    } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !extra_hours || !extra_date) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب تعبئتها' });
    }

    // التحقق من صحة عدد الساعات
    if (parseFloat(extra_hours) <= 0) {
      return res.status(400).json({ error: 'عدد الساعات الإضافية يجب أن يكون أكبر من صفر' });
    }

    // الحصول على البيانات القديمة قبل التعديل
    const [oldDataResult] = await pool.promise().query(
      'SELECT * FROM extra_hours WHERE id = ?',
      [extraHourId]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ error: 'السجل غير موجود' });
    }

    const oldData = oldDataResult[0];

    // الحصول على بيانات الموظف
    const [employeeResult] = await pool.promise().query(
      'SELECT full_name, department FROM employees WHERE code = ?',
      [employee_code]
    );

    if (employeeResult.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    const employeeName = employeeResult[0].full_name;
    const department = employeeResult[0].department;

    // البيانات الجديدة
    const newData = {
      employee_code,
      employee_name: employeeName,
      department,
      extra_hours: parseFloat(extra_hours),
      extra_date,
      notes: notes || null
    };

    // تنظيف البيانات ومعالجة حقول التاريخ
    const cleanedData = cleanUpdateData(newData);

    // تحضير استعلام التحديث
    const { setClause, values } = prepareUpdateQuery(cleanedData);

    const [result] = await pool.promise().query(
      `UPDATE extra_hours SET ${setClause} WHERE id = ?`,
      [...values, extraHourId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'السجل غير موجود' });
    }

    // إنشاء رسالة التعديل المفصلة
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'الإدارة',
      extra_hours: 'عدد الساعات',
      extra_date: 'تاريخ الإضافي',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `ساعات إضافية للموظف: ${employeeName} (كود: ${employee_code})`,
      oldData,
      newData,
      fieldLabels
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'extra_hours',
      record_id: extraHourId.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث الساعات الإضافية بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في تحديث الساعات الإضافية' });
  }
});

// حذف ساعات إضافية
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const extraHourId = req.params.id;

    // الحصول على معلومات السجل قبل الحذف
    const [extraHourData] = await pool.promise().query(
      'SELECT employee_code, employee_name, extra_hours, extra_date FROM extra_hours WHERE id = ?',
      [extraHourId]
    );

    if (extraHourData.length === 0) {
      return res.status(404).json({ error: 'السجل غير موجود' });
    }

    const extraHour = extraHourData[0];

    // حذف السجل
    const [result] = await pool.promise().query(
      'DELETE FROM extra_hours WHERE id = ?',
      [extraHourId]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'extra_hours',
      record_id: extraHourId.toString(),
      message: `تم حذف ساعات إضافية للموظف: ${extraHour.employee_name} (كود: ${extraHour.employee_code}) - عدد الساعات: ${extraHour.extra_hours} ساعة - التاريخ: ${extraHour.extra_date}`
    });

    res.json({ message: 'تم حذف الساعات الإضافية بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الساعات الإضافية:', error);
    res.status(500).json({ error: 'فشل في حذف الساعات الإضافية' });
  }
});

// الحصول على الساعات الإضافية حسب الموظف
router.get('/employee/:code', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { code } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT 
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted
      FROM extra_hours eh
      WHERE eh.employee_code = ?
      ORDER BY eh.created_at DESC, eh.id DESC
    `, [code]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب ساعات الموظف الإضافية:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات ساعات الموظف الإضافية' });
  }
});

// الحصول على الساعات الإضافية حسب الإدارة
router.get('/department/:department', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { department } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT 
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted
      FROM extra_hours eh
      WHERE eh.department = ?
      ORDER BY eh.created_at DESC, eh.id DESC
    `, [department]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب ساعات الإدارة الإضافية:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات ساعات الإدارة الإضافية' });
  }
});

// الحصول على الساعات الإضافية حسب نطاق التاريخ
router.get('/date-range', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { start_date, end_date } = req.query;
    
    if (!start_date || !end_date) {
      return res.status(400).json({ error: 'يجب تحديد تاريخ البداية والنهاية' });
    }
    
    const [rows] = await pool.promise().query(`
      SELECT 
        eh.*,
        DATE_FORMAT(eh.extra_date, '%Y-%m-%d') as extra_date_formatted
      FROM extra_hours eh
      WHERE eh.extra_date BETWEEN ? AND ?
      ORDER BY eh.created_at DESC, eh.id DESC
    `, [start_date, end_date]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الساعات الإضافية حسب التاريخ:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات الساعات الإضافية' });
  }
});

module.exports = router;
