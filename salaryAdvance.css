/* تنسيقات خاصة بقسم السلف فقط - لا تتعارض مع التنسيق العام */

/* تنسيقات أزرار التبديل */
.tab-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.tab-btn {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-btn:hover {
  background: linear-gradient(135deg, #495057, #343a40);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tab-btn.active {
  background: linear-gradient(135deg, #007bff, #0056b3);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.tab-btn i {
  font-size: 16px;
}

/* تنسيقات محتوى التبويبات */
.tab-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* تنسيقات خاصة بنموذج السلفة فقط */
.advance-form h2::before {
  content: '\f53a';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  color: #007bff;
  margin-left: 10px;
}

/* تنسيقات خاصة بجداول السلف فقط */
.advances-table-container h3::before {
  content: '\f0ce';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  margin-left: 10px;
}

/* تنسيق الصف المميز (آخر إضافة) لجدول السلف */
.advances-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.advances-table tr:first-child td {
  font-weight: bold;
}

/* لا حاجة لتنسيقات جداول إضافية - سيتم استخدام التنسيق العام */

/* تنسيقات بطاقات الإحصائيات الاحترافية */
.stats-card {
  background: #ffffff;
  border: 1px solid #e3e6f0;
  border-radius: 10px;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  height: 100%;
  overflow: hidden;
}

.stats-card-success {
  border-left: 4px solid #1cc88a;
}

.stats-card:not(.stats-card-success) {
  border-left: 4px solid #4e73df;
}

.stats-card-body {
  padding: 1.5rem;
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem;
  font-size: 1.5rem;
}

.stats-card:not(.stats-card-success) .stats-icon {
  background-color: #4e73df;
  color: white;
}

.stats-card-success .stats-icon {
  background-color: #1cc88a;
  color: white;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 2rem;
  font-weight: 700;
  color: #5a5c69;
  margin: 0;
  line-height: 1;
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #858796;
  text-transform: uppercase;
  margin: 0.25rem 0 0 0;
}

/* تنسيقات أزرار التصدير */
.export-buttons-container {
  background: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 10px;
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.export-title {
  color: #5a5c69;
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  text-transform: uppercase;
}

.export-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.export-buttons .btn {
  flex: 1;
  min-width: 140px;
  font-weight: 600;
  border-width: 2px;
}

/* تنسيقات الاستجابة */
@media (max-width: 768px) {
  .tab-buttons {
    flex-direction: column;
  }

  .export-buttons {
    flex-direction: column;
  }

  .export-buttons .btn {
    min-width: auto;
  }

  .stats-card-body {
    padding: 1rem;
  }

  .stats-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .stats-number {
    font-size: 1.5rem;
  }
}

/* تنسيق فلاتر البحث المحددة */
.filtered-search-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.search-filters-row .form-group {
  display: flex;
  flex-direction: column;
}

.search-filters-row .form-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

.filter-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
}

.filter-actions .reset-btn,
.filter-actions .export-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.filter-actions .reset-btn {
  background-color: #6c757d;
  color: white;
}

.filter-actions .reset-btn:hover {
  background-color: #5a6268;
}

.filter-actions .export-btn {
  background-color: #28a745;
  color: white;
}

.filter-actions .export-btn:hover {
  background-color: #218838;
}