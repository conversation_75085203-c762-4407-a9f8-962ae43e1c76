const express = require('express');
const router = express.Router();
const { logAction, createEditMessage } = require('../activityLogger');
const { pool } = require('../config/database');
const { authenticateToken, checkPermission } = require('../middleware/auth');

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let resignationsTableCreated = false;

// إنشاء جدول الاستقالات إذا لم يكن موجوداً
const createResignationsTable = async () => {
  if (resignationsTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً


  try {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS resignations (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'كود الموظف',
        employee_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الموظف',
        department varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'الإدارة',
        reason varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'سبب الاستقالة',
        resignation_date date NOT NULL COMMENT 'تاريخ الاستقالة',
        work_end_date date DEFAULT NULL COMMENT 'تاريخ ترك العمل',
        recommendation text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
        notes text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'ملاحظات',
        created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
        updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_department (department),
        KEY idx_resignation_date (resignation_date)
      ) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الاستقالات'
    `;

    await pool.promise().query(createTableQuery);

    resignationsTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول الاستقالات:', error);
    throw error;
  }
};

// إنشاء جدول الاستقالات
router.get('/setup-resignations-table', async (req, res) => {
  try {
    await createResignationsTable();
    res.json({ message: 'تم إنشاء جدول الاستقالات بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول الاستقالات:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول الاستقالات' });
  }
});

// الحصول على جميع الاستقالات
router.get('/', authenticateToken, async (req, res) => {
  try {
    await createResignationsTable();
    
    const [rows] = await pool.promise().query(
      "SELECT * FROM resignations ORDER BY created_at DESC, id DESC"
    );
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الاستقالات:', error);
    res.status(500).json({ error: 'فشل في جلب الاستقالات' });
  }
});

// الحصول على استقالة محددة
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    await createResignationsTable();
    
    const { id } = req.params;
    const [rows] = await pool.promise().query(
      "SELECT * FROM resignations WHERE id = ?",
      [id]
    );
    
    if (rows.length === 0) {
      return res.status(404).json({ error: 'الاستقالة غير موجودة' });
    }
    
    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب الاستقالة:', error);
    res.status(500).json({ error: 'فشل في جلب الاستقالة' });
  }
});

// إضافة استقالة جديدة
router.post('/', authenticateToken, async (req, res) => {
  try {
    await createResignationsTable();

    const {
      employee_code,
      employee_name,
      department,
      reason,
      resignation_date,
      work_end_date,
      recommendation,
      notes
    } = req.body;

    // تحويل القيم الفارغة إلى null
    const processedWorkEndDate = work_end_date && work_end_date.trim() !== '' ? work_end_date : null;

    console.log('البيانات المستلمة:', {
      employee_code,
      employee_name,
      department,
      reason,
      resignation_date,
      work_end_date,
      processedWorkEndDate,
      recommendation,
      notes
    });

    if (!employee_code || !employee_name || !reason || !resignation_date || !recommendation) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب ملؤها' });
    }

    // التحقق من عدم وجود استقالة سابقة لنفس الموظف
    const [existingResignation] = await pool.promise().query(
      "SELECT id FROM resignations WHERE employee_code = ?",
      [employee_code]
    );

    if (existingResignation.length > 0) {
      return res.status(400).json({ error: 'يوجد استقالة مسجلة مسبقاً لهذا الموظف' });
    }

    // التحقق من وجود الموظف وأنه نشط
    const [employeeCheck] = await pool.promise().query(
      "SELECT code, status FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeCheck.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    if (employeeCheck[0].status === 'مستقيل') {
      return res.status(400).json({ error: 'الموظف مستقيل بالفعل' });
    }

    // بدء معاملة قاعدة البيانات
    const connection = await pool.promise().getConnection();
    await connection.beginTransaction();

    try {
      // إضافة الاستقالة
      const [result] = await connection.query(
        `INSERT INTO resignations (
          employee_code, employee_name, department, reason,
          resignation_date, work_end_date, recommendation, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [employee_code, employee_name, department, reason, resignation_date, processedWorkEndDate, recommendation, notes]
      );

      // تحديث حالة الموظف إلى مستقيل
      await connection.query(
        "UPDATE employees SET status = 'مستقيل' WHERE code = ?",
        [employee_code]
      );

      // تأكيد المعاملة
      await connection.commit();

      // تسجيل النشاط
      await logAction({
        user_id: req.user?.id || null,
        username: req.user?.username || 'مجهول',
        action_type: 'add',
        module: 'resignations',
        record_id: result.insertId.toString(),
        message: `تم إضافة استقالة للموظف: ${employee_name} (كود: ${employee_code}) بتاريخ: ${resignation_date} لسبب: ${reason}`
      });

      res.status(201).json({
        id: result.insertId,
        employee_code,
        employee_name,
        message: 'تم إضافة الاستقالة وتحديث حالة الموظف بنجاح'
      });
    } catch (transactionError) {
      // إلغاء المعاملة في حالة الخطأ
      await connection.rollback();
      throw transactionError;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('خطأ في إضافة الاستقالة:', error);
    res.status(500).json({ error: 'فشل في إضافة الاستقالة' });
  }
});

// تحديث استقالة
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    await createResignationsTable();

    const { id } = req.params;
    const { reason, resignation_date, work_end_date, recommendation, notes } = req.body;

    // تحويل القيم الفارغة إلى null
    const processedWorkEndDate = work_end_date && work_end_date.trim() !== '' ? work_end_date : null;
    const processedResignationDate = resignation_date && resignation_date.trim() !== '' ? resignation_date : null;



    if (!reason || !resignation_date || !recommendation) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب ملؤها' });
    }

    // الحصول على البيانات القديمة قبل التعديل
    const [oldDataResult] = await pool.promise().query(
      'SELECT * FROM resignations WHERE id = ?',
      [id]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ error: 'الاستقالة غير موجودة' });
    }

    const oldData = oldDataResult[0];

    const [result] = await pool.promise().query(
      `UPDATE resignations SET
        reason = ?, resignation_date = ?, work_end_date = ?, recommendation = ?, notes = ?
      WHERE id = ?`,
      [reason, processedResignationDate, processedWorkEndDate, recommendation, notes, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'الاستقالة غير موجودة' });
    }

    // البيانات الجديدة
    const newData = {
      reason,
      resignation_date,
      work_end_date: processedWorkEndDate,
      recommendation,
      notes
    };

    // إنشاء رسالة التعديل المفصلة
    const fieldLabels = {
      reason: 'سبب الاستقالة',
      resignation_date: 'تاريخ الاستقالة',
      work_end_date: 'تاريخ ترك العمل',
      recommendation: 'التوصية',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage('استقالة', oldData, newData, fieldLabels);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'resignations',
      record_id: id.toString(),
      message: `تم تعديل استقالة للموظف: ${oldData.employee_name} (كود: ${oldData.employee_code}) - ${editMessage}`
    });

    res.json({ message: 'تم تحديث الاستقالة بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الاستقالة:', error);
    res.status(500).json({ error: 'فشل في تحديث الاستقالة' });
  }
});

// حذف استقالة
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    await createResignationsTable();

    const { id } = req.params;

    // الحصول على بيانات الاستقالة قبل الحذف
    const [resignationData] = await pool.promise().query(
      "SELECT employee_code, employee_name, reason, resignation_date, recommendation FROM resignations WHERE id = ?",
      [id]
    );

    if (resignationData.length === 0) {
      return res.status(404).json({ error: 'الاستقالة غير موجودة' });
    }

    const resignation = resignationData[0];
    const employee_code = resignation.employee_code;

    // بدء معاملة قاعدة البيانات
    const connection = await pool.promise().getConnection();
    await connection.beginTransaction();

    try {
      // حذف الاستقالة
      await connection.query("DELETE FROM resignations WHERE id = ?", [id]);

      // إعادة تفعيل الموظف
      await connection.query(
        "UPDATE employees SET status = 'نشط' WHERE code = ?",
        [employee_code]
      );

      // تأكيد المعاملة
      await connection.commit();

      // تنسيق التاريخ بصيغة مقروءة
      const formattedDate = new Date(resignation.resignation_date).toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // تسجيل النشاط
      await logAction({
        user_id: req.user?.id || null,
        username: req.user?.username || 'مجهول',
        action_type: 'delete',
        module: 'resignations',
        record_id: id.toString(),
        message: `تم حذف استقالة للموظف: ${resignation.employee_name} (كود: ${employee_code}) - السبب: ${resignation.reason} - التاريخ: ${formattedDate} - التوصية: ${resignation.recommendation} وإعادة تفعيل الموظف`
      });

      res.json({ message: 'تم حذف الاستقالة وإعادة تفعيل الموظف بنجاح' });
    } catch (transactionError) {
      // إلغاء المعاملة في حالة الخطأ
      await connection.rollback();
      throw transactionError;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('خطأ في حذف الاستقالة:', error);
    res.status(500).json({ error: 'فشل في حذف الاستقالة' });
  }
});

// إعادة تفعيل موظف مستقيل
router.post('/reactivate/:employee_code', async (req, res) => {
  try {
    const { employee_code } = req.params;

    // التحقق من وجود الموظف وأنه مستقيل
    const [employeeCheck] = await pool.promise().query(
      "SELECT code, status, full_name FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeCheck.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    if (employeeCheck[0].status !== 'مستقيل') {
      return res.status(400).json({ error: 'الموظف ليس مستقيلاً' });
    }

    // التحقق من أن التوصية تسمح بالعودة
    const [resignationCheck] = await pool.promise().query(
      "SELECT recommendation FROM resignations WHERE employee_code = ?",
      [employee_code]
    );

    if (resignationCheck.length === 0) {
      return res.status(400).json({ error: 'لا توجد استقالة مسجلة لهذا الموظف' });
    }

    if (resignationCheck[0].recommendation !== 'يوصى بعودته') {
      return res.status(400).json({ error: 'لا يمكن إعادة تفعيل هذا الموظف - التوصية لا تسمح بالعودة' });
    }

    // إعادة تفعيل الموظف
    const [result] = await pool.promise().query(
      "UPDATE employees SET status = 'نشط' WHERE code = ?",
      [employee_code]
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({ error: 'فشل في إعادة تفعيل الموظف' });
    }

    res.json({
      message: `تم إعادة تفعيل الموظف ${employeeCheck[0].full_name} بنجاح`,
      employee_code,
      employee_name: employeeCheck[0].full_name
    });
  } catch (error) {
    console.error('خطأ في إعادة تفعيل الموظف:', error);
    res.status(500).json({ error: 'فشل في إعادة تفعيل الموظف' });
  }
});

// الحصول على استقالات موظف محدد
router.get('/employee/:employee_code', authenticateToken, checkPermission('view_resignations'), async (req, res) => {
  try {
    await createResignationsTable();
    
    const { employee_code } = req.params;
    const [rows] = await pool.promise().query(
      "SELECT * FROM resignations WHERE employee_code = ? ORDER BY created_at DESC, id DESC",
      [employee_code]
    );
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب استقالات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب استقالات الموظف' });
  }
});

// الحصول على إحصائيات الاستقالات
router.get('/stats/summary', authenticateToken, checkPermission('view_resignation_reports'), async (req, res) => {
  try {
    await createResignationsTable();
    
    const [totalResult] = await pool.promise().query(
      "SELECT COUNT(*) as total FROM resignations"
    );
    
    const [recommendedResult] = await pool.promise().query(
      "SELECT COUNT(*) as recommended FROM resignations WHERE recommendation = 'يوصى بعودته'"
    );
    
    const [notRecommendedResult] = await pool.promise().query(
      "SELECT COUNT(*) as not_recommended FROM resignations WHERE recommendation = 'لا يوصى بعودته'"
    );
    
    const [reasonsResult] = await pool.promise().query(
      "SELECT reason, COUNT(*) as count FROM resignations GROUP BY reason ORDER BY count DESC"
    );
    
    const [departmentsResult] = await pool.promise().query(
      "SELECT department, COUNT(*) as count FROM resignations GROUP BY department ORDER BY count DESC"
    );
    
    res.json({
      total: totalResult[0].total,
      recommended: recommendedResult[0].recommended,
      not_recommended: notRecommendedResult[0].not_recommended,
      by_reason: reasonsResult,
      by_department: departmentsResult
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الاستقالات:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات الاستقالات' });
  }
});

module.exports = router;
