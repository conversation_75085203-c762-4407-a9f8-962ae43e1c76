// نظام إدارة السلف
document.addEventListener('DOMContentLoaded', function() {
  // متغيرات عامة
  let API_URL = localStorage.getItem('serverUrl') || "http://localhost:5500/api";
  let employees = [];
  let advances = [];
  let currentEditId = null;
  
  // عناصر DOM
  const employeeSearchAdd = document.getElementById('employeeSearchAdd');
  const employeeCode = document.getElementById('employeeCode');
  const employeeName = document.getElementById('employeeName');
  const employeeDepartment = document.getElementById('employeeDepartment');
  const advanceAmount = document.getElementById('advanceAmount');
  const advanceDate = document.getElementById('advanceDate');
  const advanceReason = document.getElementById('advanceReason');
  const paymentMethod = document.getElementById('paymentMethod');
  const notes = document.getElementById('notes');
  const saveAdvanceBtn = document.getElementById('saveAdvance');
  const resetFormBtn = document.getElementById('resetForm');
  
  // عناصر التقارير
  const filterStartDate = document.getElementById('filterStartDate');
  const filterEndDate = document.getElementById('filterEndDate');
  const filterDepartment = document.getElementById('filterDepartment');
  const filterEmployee = document.getElementById('filterEmployee');
  const applyFiltersBtn = document.getElementById('applyFilters');
  const clearFiltersBtn = document.getElementById('clearFilters');
  const printReportBtn = document.getElementById('printReport');
  const exportExcelBtn = document.getElementById('exportExcel');
  
  // عناصر النافذة المنبثقة
  const editModal = document.getElementById('editAdvanceModal');
  const updateAdvanceBtn = document.getElementById('updateAdvance');
  
  // تهيئة الصفحة
  init();
  
  async function init() {
    try {


      // إنشاء جدول السلف
      await createSalaryAdvanceTable();

      // تحميل البيانات

      await loadEmployees();


      await loadAdvances();

      // تهيئة الأحداث

      setupEventListeners();

      // تطبيق الصلاحيات

      applyPermissions();

      // تعيين التاريخ الحالي
      if (advanceDate) {
        advanceDate.value = new Date().toISOString().split('T')[0];
      }



    } catch (error) {

      showNotification('حدث خطأ في تحميل الصفحة: ' + error.message, 'error');
    }
  }
  
  // إنشاء جدول السلف
  async function createSalaryAdvanceTable() {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/salary-advances/create-table`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const result = await response.json();
      console.log('تم التحقق من جدول السلف:', result.message);
    } catch (error) {
      console.error('خطأ في إنشاء جدول السلف:', error);
    }
  }
  
  // تحميل بيانات الموظفين
  async function loadEmployees() {
    try {
      // إضافة معامل لاستبعاد الموظفين المستقيلين
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/employees?include_resigned=false`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        employees = await response.json();
        populateDepartmentFilter();
      } else {
        throw new Error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات الموظفين:', error);
      showNotification('فشل في تحميل بيانات الموظفين', 'error');
    }
  }
  
  // تحميل بيانات السلف
  async function loadAdvances() {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/salary-advances`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.ok) {
        advances = await response.json();
        console.log('تم تحميل السلف:', advances.length);
        displayAdvances(advances);
        displayReports(advances);
      } else {
        throw new Error('فشل في تحميل بيانات السلف');
      }
    } catch (error) {
      console.error('خطأ في تحميل السلف:', error);
      showNotification('فشل في تحميل بيانات السلف', 'error');
    }
  }
  
  // تهيئة مستمعي الأحداث
  function setupEventListeners() {
    // أحداث التبويبات
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });
    
    // أحداث البحث عن الموظف
    if (employeeSearchAdd) {
      // معالجة الكتابة في حقل البحث
      employeeSearchAdd.addEventListener('input', function() {
        handleEmployeeSearch(this.value, 'employeeSearchSuggestions');
      });

      // معالجة اختيار موظف من القائمة
      employeeSearchAdd.addEventListener('change', function() {
        handleEmployeeSelection(this.value);
      });

      // معالجة النقر على خيار من القائمة
      employeeSearchAdd.addEventListener('blur', function() {
        // تأخير قصير للسماح بمعالجة النقر على الخيار
        setTimeout(() => {
          if (this.value) {
            handleEmployeeSelection(this.value);
          }
        }, 200);
      });
    }
    
    // أحداث النموذج
    if (saveAdvanceBtn) {
      saveAdvanceBtn.addEventListener('click', saveAdvance);
    }
    
    if (resetFormBtn) {
      resetFormBtn.addEventListener('click', resetForm);
    }
    
    // أحداث التقارير
    if (applyFiltersBtn) {
      applyFiltersBtn.addEventListener('click', applyFilters);
    }
    
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', clearFilters);
    }
    
    if (printReportBtn) {
      printReportBtn.addEventListener('click', printReport);
    }
    
    if (exportExcelBtn) {
      exportExcelBtn.addEventListener('click', exportToExcel);
    }
    
    // أحداث النافذة المنبثقة
    setupModalEvents();
    
    // أحداث البحث في التقارير
    if (filterEmployee) {
      filterEmployee.addEventListener('input', function() {
        handleEmployeeSearch(this.value, 'filterEmployeeSuggestions');
      });
    }
    
    // أحداث البحث في التعديل
    const editEmployeeSearch = document.getElementById('editEmployeeSearch');
    if (editEmployeeSearch) {
      editEmployeeSearch.addEventListener('input', function() {
        handleEmployeeSearch(this.value, 'editEmployeeSuggestions');
      });
      editEmployeeSearch.addEventListener('change', function() {
        handleEditEmployeeSelection(this.value);
      });
      editEmployeeSearch.addEventListener('blur', function() {
        setTimeout(() => {
          if (this.value) {
            handleEditEmployeeSelection(this.value);
          }
        }, 200);
      });
    }
  }
  
  // تطبيع النص العربي (إزالة الهمزات والتشكيل وتوحيد التاء المربوطة والمفتوحة)
  function normalizeArabicText(text) {
    if (!text) return '';

    // تحويل النص إلى حروف صغيرة
    let normalized = text.toLowerCase();

    // استبدال الهمزات المختلفة بألف عادية
    normalized = normalized.replace(/[أإآ]/g, 'ا');

    // استبدال التاء المربوطة بتاء مفتوحة
    normalized = normalized.replace(/ة/g, 'ه');

    // إزالة التشكيل
    normalized = normalized.replace(/[\u064B-\u0652]/g, '');

    // إزالة المسافات الزائدة
    normalized = normalized.trim().replace(/\s+/g, ' ');

    return normalized;
  }

  // البحث عن الموظف
  function handleEmployeeSearch(searchTerm, datalistId) {
    const datalist = document.getElementById(datalistId);
    if (!datalist) {
      console.error(`لم يتم العثور على datalist بالمعرف: ${datalistId}`);
      return;
    }

    datalist.innerHTML = '';

    // التحقق من وجود بيانات الموظفين
    if (!employees || employees.length === 0) {
      const option = document.createElement('option');
      option.value = "لا توجد بيانات موظفين";
      datalist.appendChild(option);
      return;
    }

    if (!searchTerm || searchTerm.trim() === '') {
      // مسح الحقول عند مسح البحث
      if (datalistId === 'employeeSearchSuggestions') {
        clearEmployeeFields();
      } else if (datalistId === 'editEmployeeSuggestions') {
        clearEditEmployeeFields();
      }
      return;
    }

    // تطبيع نص البحث
    const normalizedSearchTerm = normalizeArabicText(searchTerm);

    // البحث في الموظفين
    const filteredEmployees = employees.filter(emp => {
      // التحقق من وجود البيانات المطلوبة
      if (!emp || (!emp.code && !emp.full_name)) {
        return false;
      }

      // البحث بالكود
      if (emp.code && emp.code.toString().includes(searchTerm)) {
        return true;
      }

      // البحث بالاسم (مع التطبيع)
      if (emp.full_name) {
        const normalizedName = normalizeArabicText(emp.full_name);
        return normalizedName.includes(normalizedSearchTerm);
      }

      return false;
    });

    console.log(`تم العثور على ${filteredEmployees.length} موظف مطابق`);

    if (filteredEmployees.length > 0) {
      // عرض أول 15 نتيجة فقط لتحسين الأداء
      filteredEmployees.slice(0, 15).forEach(emp => {
        const option = document.createElement('option');
        option.value = `${emp.code} - ${emp.full_name}`;
        datalist.appendChild(option);
      });
    } else {
      // إضافة رسالة إذا لم يتم العثور على نتائج
      const option = document.createElement('option');
      option.value = "لا توجد نتائج مطابقة";
      datalist.appendChild(option);
    }
  }
  
  // معالجة اختيار الموظف
  function handleEmployeeSelection(searchValue) {
    console.log(`معالجة اختيار الموظف: ${searchValue}`);

    if (!searchValue || searchValue.trim() === '' || searchValue === 'لا توجد نتائج مطابقة') {
      clearEmployeeFields();
      return;
    }

    // البحث بالكود أولاً
    const codeMatch = searchValue.match(/^(\d+)/);
    if (codeMatch) {
      const employeeCode = codeMatch[1];
      const employee = employees.find(emp => emp.code.toString() === employeeCode);

      if (employee) {
        console.log(`تم العثور على الموظف: ${employee.full_name}`);
        fillEmployeeFields(employee);
        return;
      }
    }

    // البحث بالاسم إذا لم يتم العثور على الموظف بالكود
    const normalizedSearchValue = normalizeArabicText(searchValue);
    const employee = employees.find(emp => {
      const normalizedName = normalizeArabicText(emp.full_name);
      return normalizedName === normalizedSearchValue ||
             emp.full_name.toLowerCase() === searchValue.toLowerCase();
    });

    if (employee) {
      console.log(`تم العثور على الموظف بالاسم: ${employee.full_name}`);
      fillEmployeeFields(employee);
      // تحديث قيمة حقل البحث لتظهر بالتنسيق الصحيح
      if (employeeSearchAdd) {
        employeeSearchAdd.value = `${employee.code} - ${employee.full_name}`;
      }
    } else {
      console.log('لم يتم العثور على الموظف');
      clearEmployeeFields();
      showNotification('الموظف غير موجود', 'error');
    }
  }
  
  // معالجة اختيار الموظف في التعديل
  function handleEditEmployeeSelection(searchValue) {
    console.log(`معالجة اختيار الموظف في التعديل: ${searchValue}`);

    if (!searchValue || searchValue.trim() === '' || searchValue === 'لا توجد نتائج مطابقة') {
      clearEditEmployeeFields();
      return;
    }

    // البحث بالكود أولاً
    const codeMatch = searchValue.match(/^(\d+)/);
    if (codeMatch) {
      const employeeCode = codeMatch[1];
      const employee = employees.find(emp => emp.code.toString() === employeeCode);

      if (employee) {
        console.log(`تم العثور على الموظف في التعديل: ${employee.full_name}`);
        fillEditEmployeeFields(employee);
        return;
      }
    }

    // البحث بالاسم إذا لم يتم العثور على الموظف بالكود
    const normalizedSearchValue = normalizeArabicText(searchValue);
    const employee = employees.find(emp => {
      const normalizedName = normalizeArabicText(emp.full_name);
      return normalizedName === normalizedSearchValue ||
             emp.full_name.toLowerCase() === searchValue.toLowerCase();
    });

    if (employee) {
      console.log(`تم العثور على الموظف بالاسم في التعديل: ${employee.full_name}`);
      fillEditEmployeeFields(employee);
      // تحديث قيمة حقل البحث لتظهر بالتنسيق الصحيح
      const editEmployeeSearch = document.getElementById('editEmployeeSearch');
      if (editEmployeeSearch) {
        editEmployeeSearch.value = `${employee.code} - ${employee.full_name}`;
      }
    } else {
      console.log('لم يتم العثور على الموظف في التعديل');
      clearEditEmployeeFields();
      showNotification('الموظف غير موجود', 'error');
    }
  }
  
  // ملء حقول الموظف
  function fillEmployeeFields(employee) {
    if (employeeCode) employeeCode.value = employee.code;
    if (employeeName) employeeName.value = employee.full_name;
    if (employeeDepartment) employeeDepartment.value = employee.department || '';
  }
  
  // ملء حقول الموظف في التعديل
  function fillEditEmployeeFields(employee) {
    const editEmployeeCode = document.getElementById('editEmployeeCode');
    const editEmployeeName = document.getElementById('editEmployeeName');
    const editEmployeeDepartment = document.getElementById('editEmployeeDepartment');
    
    if (editEmployeeCode) editEmployeeCode.value = employee.code;
    if (editEmployeeName) editEmployeeName.value = employee.full_name;
    if (editEmployeeDepartment) editEmployeeDepartment.value = employee.department || '';
  }
  
  // مسح حقول الموظف
  function clearEmployeeFields() {
    if (employeeCode) employeeCode.value = '';
    if (employeeName) employeeName.value = '';
    if (employeeDepartment) employeeDepartment.value = '';
  }
  
  // مسح حقول الموظف في التعديل
  function clearEditEmployeeFields() {
    const editEmployeeCode = document.getElementById('editEmployeeCode');
    const editEmployeeName = document.getElementById('editEmployeeName');
    const editEmployeeDepartment = document.getElementById('editEmployeeDepartment');
    
    if (editEmployeeCode) editEmployeeCode.value = '';
    if (editEmployeeName) editEmployeeName.value = '';
    if (editEmployeeDepartment) editEmployeeDepartment.value = '';
  }
  
  // حفظ السلفة
  async function saveAdvance() {
    try {
      // التحقق من البيانات
      if (!validateAdvanceForm()) {
        return;
      }
      
      const advanceData = {
        employee_code: employeeCode.value,
        advance_amount: parseFloat(advanceAmount.value),
        advance_date: advanceDate.value,
        advance_reason: advanceReason.value,
        payment_method: paymentMethod.value,
        notes: notes.value || null
      };
      
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/salary-advances`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(advanceData)
      });
      
      const result = await response.json();
      
      if (response.ok) {
        showNotification('تم حفظ السلفة بنجاح', 'success');
        resetForm();
        await loadAdvances();
      } else {
        throw new Error(result.error || 'فشل في حفظ السلفة');
      }
    } catch (error) {
      console.error('خطأ في حفظ السلفة:', error);
      showNotification(error.message, 'error');
    }
  }
  
  // التحقق من صحة النموذج
  function validateAdvanceForm() {
    if (!employeeCode.value) {
      showNotification('يجب اختيار موظف', 'error');
      employeeSearchAdd.focus();
      return false;
    }
    
    if (!advanceAmount.value || parseFloat(advanceAmount.value) <= 0) {
      showNotification('يجب إدخال قيمة سلفة صحيحة', 'error');
      advanceAmount.focus();
      return false;
    }
    
    if (!advanceDate.value) {
      showNotification('يجب تحديد تاريخ السلفة', 'error');
      advanceDate.focus();
      return false;
    }
    
    if (!advanceReason.value.trim()) {
      showNotification('يجب إدخال سبب السلفة', 'error');
      advanceReason.focus();
      return false;
    }
    
    if (!paymentMethod.value.trim()) {
      showNotification('يجب إدخال طريقة السداد', 'error');
      paymentMethod.focus();
      return false;
    }
    
    return true;
  }
  
  // إعادة تعيين النموذج
  function resetForm() {
    if (employeeSearchAdd) employeeSearchAdd.value = '';
    if (employeeCode) employeeCode.value = '';
    if (employeeName) employeeName.value = '';
    if (employeeDepartment) employeeDepartment.value = '';
    if (advanceAmount) advanceAmount.value = '';
    if (advanceDate) advanceDate.value = new Date().toISOString().split('T')[0];
    if (advanceReason) advanceReason.value = '';
    if (paymentMethod) paymentMethod.value = '';
    if (notes) notes.value = '';
    
    // مسح قائمة الاقتراحات
    const datalist = document.getElementById('employeeSearchSuggestions');
    if (datalist) {
      datalist.innerHTML = '';
    }
  }
  
  // عرض السلف في الجدول
  function displayAdvances(advancesData) {
    const tableBody = document.getElementById('advancesTableBody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    if (advancesData.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="9" class="text-center">لا توجد سلف مسجلة</td></tr>';
      return;
    }

    // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
    const sortedAdvances = [...advancesData].sort((a, b) => {
      // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
      const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
      const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
      return bTime - aTime; // الأحدث أولاً
    });

    sortedAdvances.forEach((advance, index) => {
      const row = document.createElement('tr');

      // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
      if (index === 0) {
        row.style.backgroundColor = '#e8f5e8';
        row.style.border = '2px solid #4CAF50';
      }

      row.innerHTML = `
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${advance.employee_code}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${advance.employee_name}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${advance.department}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatAmount(advance.advance_amount)}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatDate(advance.advance_date)}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${advance.advance_reason}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${advance.payment_method}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${advance.notes || '-'}</td>
        <td>
          <div class="action-buttons">
            ${hasPermission('edit_salary_advance') ? `<button class="action-btn edit-btn" onclick="editAdvance(${advance.id})">
              <i class="fas fa-edit"></i>
              تعديل
            </button>` : ''}
            ${hasPermission('delete_salary_advance') ? `<button class="action-btn delete-btn" onclick="deleteAdvance(${advance.id})">
              <i class="fas fa-trash"></i>
              حذف
            </button>` : ''}
          </div>
        </td>
      `;
      tableBody.appendChild(row);
    });

    // تطبيق الصلاحيات على أزرار الإجراءات
    applyPermissions();
  }
  

  
  // ملء قائمة الإدارات في الفلاتر
  function populateDepartmentFilter() {
    if (!filterDepartment) return;
    
    const departments = [...new Set(employees.map(emp => emp.department).filter(dept => dept))];
    
    filterDepartment.innerHTML = '<option value="">جميع الإدارات</option>';
    departments.forEach(dept => {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      filterDepartment.appendChild(option);
    });
  }
  
  // تطبيق الفلاتر
  function applyFilters() {
    let filteredAdvances = [...advances];

    // فلتر التاريخ - إصلاح مشكلة النقص يوم
    if (filterStartDate.value) {
      const startDateFilter = new Date(filterStartDate.value);
      startDateFilter.setHours(0, 0, 0, 0);
      filteredAdvances = filteredAdvances.filter(advance => {
        const advanceDate = new Date(advance.advance_date);
        advanceDate.setHours(0, 0, 0, 0);
        return advanceDate >= startDateFilter;
      });
    }

    if (filterEndDate.value) {
      const endDateFilter = new Date(filterEndDate.value);
      endDateFilter.setHours(23, 59, 59, 999);
      filteredAdvances = filteredAdvances.filter(advance => {
        const advanceDate = new Date(advance.advance_date);
        advanceDate.setHours(0, 0, 0, 0);
        return advanceDate <= endDateFilter;
      });
    }

    // فلتر الإدارة
    if (filterDepartment.value) {
      filteredAdvances = filteredAdvances.filter(advance =>
        advance.department === filterDepartment.value
      );
    }

    // فلتر الموظف
    if (filterEmployee.value) {
      const searchTerm = filterEmployee.value.toLowerCase();
      filteredAdvances = filteredAdvances.filter(advance =>
        advance.employee_name.toLowerCase().includes(searchTerm) ||
        advance.employee_code.toString().includes(searchTerm)
      );
    }

    displayReports(filteredAdvances);
  }
  
  // مسح الفلاتر
  function clearFilters() {
    if (filterStartDate) filterStartDate.value = '';
    if (filterEndDate) filterEndDate.value = '';
    if (filterDepartment) filterDepartment.value = '';
    if (filterEmployee) filterEmployee.value = '';
    
    displayReports(advances);
  }
  
  // عرض التقارير
  function displayReports(reportsData) {
    const tableBody = document.getElementById('reportsTableBody');
    const totalAdvances = document.getElementById('totalAdvances');
    const totalAmount = document.getElementById('totalAmount');
    
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    if (reportsData.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="8" class="text-center">لا توجد بيانات للعرض</td></tr>';
      if (totalAdvances) totalAdvances.textContent = '0';
      if (totalAmount) totalAmount.textContent = '0.00';
      return;
    }
    
    let totalAmountValue = 0;
    
    reportsData.forEach(advance => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${advance.employee_code}</td>
        <td>${advance.employee_name}</td>
        <td>${advance.department}</td>
        <td>${formatAmount(advance.advance_amount)}</td>
        <td>${formatDate(advance.advance_date)}</td>
        <td>${advance.advance_reason}</td>
        <td>${advance.payment_method}</td>
        <td>${advance.notes || '-'}</td>
      `;
      tableBody.appendChild(row);
      
      totalAmountValue += parseFloat(advance.advance_amount);
    });
    
    // تحديث الإحصائيات
    if (totalAdvances) totalAdvances.textContent = reportsData.length;
    if (totalAmount) totalAmount.textContent = formatAmount(totalAmountValue);
  }
  
  // تنسيق التاريخ
  function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    // عرض التاريخ بالتنسيق الميلادي
    return date.toLocaleDateString('en-GB'); // DD/MM/YYYY
  }

  // تنسيق المبلغ
  function formatAmount(amount) {
    if (!amount) return '0';
    const num = parseFloat(amount);
    // إذا كان الرقم صحيح (بدون كسور) اعرضه بدون .00
    if (num % 1 === 0) {
      return num.toString();
    }
    // إذا كان له كسور اعرضه بالكسور
    return num.toFixed(2);
  }
  
  // استخدام دالة showNotification من shared-utils.js
  
  // تطبيق الصلاحيات
  function applyPermissions() {
    if (typeof PermissionManager !== 'undefined') {
      const permissionManager = new PermissionManager();
      permissionManager.applyPermissions();
    }
  }
  
  // فحص الصلاحيات
  function hasPermission(permission) {
    try {
      const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
      const result = permissions[permission] === true;
      console.log(`[SalaryAdvance] hasPermission(${permission}) = ${result}`, permissions);
      return result;
    } catch (error) {
      console.error('خطأ في قراءة الصلاحيات:', error);
      return false;
    }
  }

  // تعديل السلفة
  async function editAdvance(id) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_salary_advance')) {
      alert('ليس لديك صلاحية لتعديل السلف');
      return;
    }

    try {
      const advance = advances.find(a => a.id === id);
      if (!advance) {
        showNotification('السلفة غير موجودة', 'error');
        return;
      }

      currentEditId = id;

      // ملء النموذج بالبيانات
      const editEmployeeSearch = document.getElementById('editEmployeeSearch');
      const editEmployeeCode = document.getElementById('editEmployeeCode');
      const editEmployeeName = document.getElementById('editEmployeeName');
      const editEmployeeDepartment = document.getElementById('editEmployeeDepartment');
      const editAdvanceAmount = document.getElementById('editAdvanceAmount');
      const editAdvanceDate = document.getElementById('editAdvanceDate');
      const editAdvanceReason = document.getElementById('editAdvanceReason');
      const editPaymentMethod = document.getElementById('editPaymentMethod');
      const editNotes = document.getElementById('editNotes');

      if (editEmployeeSearch) editEmployeeSearch.value = `${advance.employee_code} - ${advance.employee_name}`;
      if (editEmployeeCode) editEmployeeCode.value = advance.employee_code;
      if (editEmployeeName) editEmployeeName.value = advance.employee_name;
      if (editEmployeeDepartment) editEmployeeDepartment.value = advance.department;
      if (editAdvanceAmount) editAdvanceAmount.value = advance.advance_amount;
      if (editAdvanceDate) {
        // إصلاح مشكلة التاريخ الناقص يوم
        const date = new Date(advance.advance_date);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        editAdvanceDate.value = `${year}-${month}-${day}`;
      }
      if (editAdvanceReason) editAdvanceReason.value = advance.advance_reason;
      if (editPaymentMethod) editPaymentMethod.value = advance.payment_method;
      if (editNotes) editNotes.value = advance.notes || '';

      // إظهار النافذة المنبثقة
      editModal.style.display = 'block';
    } catch (error) {
      console.error('خطأ في تعديل السلفة:', error);
      showNotification('حدث خطأ أثناء تحميل بيانات السلفة', 'error');
    }
  }

  // حذف السلفة
  async function deleteAdvance(id) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_salary_advance')) {
      alert('ليس لديك صلاحية لحذف السلف');
      return;
    }

    if (!confirm('هل أنت متأكد من حذف هذه السلفة؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/salary-advances/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();

      if (response.ok) {
        showNotification('تم حذف السلفة بنجاح', 'success');
        await loadAdvances();
      } else {
        throw new Error(result.error || 'فشل في حذف السلفة');
      }
    } catch (error) {
      console.error('خطأ في حذف السلفة:', error);
      showNotification(error.message, 'error');
    }
  }

  // تحديث السلفة
  async function updateAdvance() {
    try {
      const editEmployeeCode = document.getElementById('editEmployeeCode');
      const editAdvanceAmount = document.getElementById('editAdvanceAmount');
      const editAdvanceDate = document.getElementById('editAdvanceDate');
      const editAdvanceReason = document.getElementById('editAdvanceReason');
      const editPaymentMethod = document.getElementById('editPaymentMethod');
      const editNotes = document.getElementById('editNotes');

      // التحقق من البيانات
      if (!editEmployeeCode.value || !editAdvanceAmount.value || !editAdvanceDate.value ||
          !editAdvanceReason.value || !editPaymentMethod.value) {
        showNotification('يجب ملء جميع الحقول المطلوبة', 'error');
        return;
      }

      const advanceData = {
        employee_code: editEmployeeCode.value,
        advance_amount: parseFloat(editAdvanceAmount.value),
        advance_date: editAdvanceDate.value,
        advance_reason: editAdvanceReason.value,
        payment_method: editPaymentMethod.value,
        notes: editNotes.value || null
      };

      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/salary-advances/${currentEditId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(advanceData)
      });

      const result = await response.json();

      if (response.ok) {
        showNotification('تم تحديث السلفة بنجاح', 'success');
        editModal.style.display = 'none';
        currentEditId = null;
        await loadAdvances();
      } else {
        throw new Error(result.error || 'فشل في تحديث السلفة');
      }
    } catch (error) {
      console.error('خطأ في تحديث السلفة:', error);
      showNotification(error.message, 'error');
    }
  }

  // إعداد أحداث النافذة المنبثقة
  function setupModalEvents() {
    // إغلاق النافذة المنبثقة
    const closeButtons = editModal.querySelectorAll('.close, .close-modal');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        editModal.style.display = 'none';
        currentEditId = null;
      });
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', (event) => {
      if (event.target === editModal) {
        editModal.style.display = 'none';
        currentEditId = null;
      }
    });

    // حفظ التغييرات
    if (updateAdvanceBtn) {
      updateAdvanceBtn.addEventListener('click', updateAdvance);
    }
  }

  // طباعة التقرير
  function printReport() {
    const printWindow = window.open('', '_blank');
    const reportsTable = document.getElementById('reportsTable');
    const totalAdvances = document.getElementById('totalAdvances').textContent;
    const totalAmount = document.getElementById('totalAmount').textContent;

    if (!reportsTable) {
      showNotification('لا توجد بيانات للطباعة', 'warning');
      return;
    }

    const printContent = `
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>تقرير السلف</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .header h1 { color: #2c3e50; margin-bottom: 10px; }
          .header p { color: #7f8c8d; margin: 5px 0; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          th { background-color: #f8f9fa; font-weight: bold; }
          .summary { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }
          .summary-item { margin: 5px 0; font-weight: bold; }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>تقرير السلف</h1>
          <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
        ${reportsTable.outerHTML}
        <div class="summary">
          <div class="summary-item">إجمالي عدد السلف: ${totalAdvances}</div>
          <div class="summary-item">إجمالي قيمة السلف: ${totalAmount}</div>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  }

  // تصدير إلى Excel
  function exportToExcel() {
    try {
      const reportsTable = document.getElementById('reportsTable');
      if (!reportsTable) {
        showNotification('لا توجد بيانات للتصدير', 'warning');
        return;
      }

      // إنشاء workbook جديد
      const wb = XLSX.utils.book_new();

      // تحويل الجدول إلى worksheet
      const ws = XLSX.utils.table_to_sheet(reportsTable);

      // إضافة الـ worksheet إلى الـ workbook
      XLSX.utils.book_append_sheet(wb, ws, 'تقرير السلف');

      // تصدير الملف
      const fileName = `تقرير_السلف_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      showNotification('تم تصدير التقرير بنجاح', 'success');
    } catch (error) {
      console.error('خطأ في تصدير Excel:', error);
      showNotification('حدث خطأ أثناء تصدير التقرير', 'error');
    }
  }

  // تعريف الوظائف العامة
  window.editAdvance = editAdvance;
  window.deleteAdvance = deleteAdvance;

  // التحقق من المحتوى المحدد من البطاقات
  setTimeout(() => {
    checkSelectedContent();
  }, 100);
});

// التحقق من المحتوى المحدد من البطاقات
function checkSelectedContent() {
  const selectedContent = localStorage.getItem('selectedSalaryAdvanceTab');

  if (selectedContent) {
    // حذف المحتوى المحفوظ
    localStorage.removeItem('selectedSalaryAdvanceTab');

    // عرض المحتوى المناسب
    showContent(selectedContent);
  } else {
    // عرض المحتوى الافتراضي (إضافة سلفة)
    showContent('add-advance');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.classList.remove('active');
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.classList.add('active');
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-advance') {
        pageTitle.textContent = 'إضافة سلفة جديدة';
      } else if (contentType === 'reports') {
        pageTitle.textContent = 'تقارير السلف';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'reports') {
      // يمكن إضافة تحميل بيانات التقارير هنا إذا لزم الأمر
    }
  } else {
    console.error('لم يتم العثور على المحتوى:', contentType);
  }
}

// إعداد البحث المباشر للسلف
function setupAdvanceFilters() {
  const filterInputs = ['filterAdvanceEmployeeCode', 'filterAdvanceEmployeeName', 'filterAdvanceFromDate', 'filterAdvanceToDate'];

  filterInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', function() {
        clearTimeout(this.filterTimeout);
        this.filterTimeout = setTimeout(() => {
          applyAdvanceFilters();
        }, 300);
      });
    }
  });

  // زر مسح الفلاتر
  const clearBtn = document.getElementById('clearAdvanceFiltersBtn');
  if (clearBtn) {
    clearBtn.addEventListener('click', clearAdvanceFilters);
  }
}

// تطبيق فلاتر السلف
function applyAdvanceFilters() {
  const employeeCode = document.getElementById('filterAdvanceEmployeeCode')?.value.trim() || '';
  const employeeName = document.getElementById('filterAdvanceEmployeeName')?.value.trim() || '';
  const fromDate = document.getElementById('filterAdvanceFromDate')?.value || '';
  const toDate = document.getElementById('filterAdvanceToDate')?.value || '';

  const tableBody = document.getElementById('advancesTableBody');
  if (!tableBody) return;

  const rows = tableBody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length === 0) return;

    // استخراج البيانات من الخلايا
    const rowEmployeeCode = cells[0]?.textContent?.trim() || '';
    const rowEmployeeName = cells[1]?.textContent?.trim() || '';
    const rowDate = cells[4]?.textContent?.trim() || '';

    let showRow = true;

    // فلترة بالكود
    if (employeeCode && !rowEmployeeCode.includes(employeeCode)) {
      showRow = false;
    }

    // فلترة بالاسم
    if (employeeName && !rowEmployeeName.toLowerCase().includes(employeeName.toLowerCase())) {
      showRow = false;
    }

    // فلترة بالتاريخ من
    if (fromDate && rowDate) {
      const rowDateObj = new Date(rowDate);
      const fromDateObj = new Date(fromDate);
      if (rowDateObj < fromDateObj) {
        showRow = false;
      }
    }

    // فلترة بالتاريخ إلى
    if (toDate && rowDate) {
      const rowDateObj = new Date(rowDate);
      const toDateObj = new Date(toDate);
      if (rowDateObj > toDateObj) {
        showRow = false;
      }
    }

    // إظهار أو إخفاء الصف
    row.style.display = showRow ? '' : 'none';
  });
}

// مسح فلاتر السلف
function clearAdvanceFilters() {
  document.getElementById('filterAdvanceEmployeeCode').value = '';
  document.getElementById('filterAdvanceEmployeeName').value = '';
  document.getElementById('filterAdvanceFromDate').value = '';
  document.getElementById('filterAdvanceToDate').value = '';

  // إظهار جميع الصفوف
  const tableBody = document.getElementById('advancesTableBody');
  if (tableBody) {
    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
      row.style.display = '';
    });
  }
}

// إضافة إعداد البحث المباشر إلى التهيئة
const originalInit = init;
init = function() {
  originalInit();
  setupAdvanceFilters();
};
