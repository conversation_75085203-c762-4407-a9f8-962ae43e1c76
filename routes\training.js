const express = require('express');
const router = express.Router();
const { logAction } = require('../activityLogger');
const { authenticateToken } = require('../middleware/auth');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

// متغير لتتبع ما إذا تم إنشاء الجدول أم لا
let trainingTableCreated = false;

// دالة لإنشاء جدول التدريب
const createTrainingTable = async (pool) => {
  if (trainingTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً

  const sql = `CREATE TABLE IF NOT EXISTS training_courses (
    id int NOT NULL AUTO_INCREMENT,
    employee_code varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'كود الموظف',
    employee_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الموظف',
    department varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'الإدارة',
    course_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الدورة',
    course_date date NOT NULL COMMENT 'تاريخ الدورة',
    course_duration int NOT NULL COMMENT 'مدة الدورة بالأيام',
    training_type enum('تدريب داخلي','تدريب خارجي','تدريب خاص') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'نوع التدريب',
    course_cost decimal(10,2) DEFAULT '0.00' COMMENT 'تكلفة الدورة',
    notes text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ملاحظات',
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    PRIMARY KEY (id),
    KEY idx_employee_code (employee_code),
    KEY idx_department (department),
    KEY idx_course_date (course_date),
    KEY idx_training_type (training_type),
    KEY idx_course_name (course_name),
    KEY idx_training_courses_employee_code (employee_code),
    KEY idx_training_courses_course_date (course_date),
    KEY idx_training_courses_training_type (training_type)
  ) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الدورات التدريبية'`;

  await pool.promise().query(sql);
  trainingTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
};

// إنشاء جدول التدريب
router.get('/create-table', async (req, res) => {
  try {
    await createTrainingTable(req.app.locals.pool);
    res.json({ message: 'تم التحقق من جدول التدريب' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول التدريب:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول التدريب' });
  }
});

// الحصول على جميع الدورات التدريبية
router.get('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);
    
    const [rows] = await pool.promise().query(
      'SELECT * FROM training_courses ORDER BY created_at DESC, id DESC'
    );
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الدورات التدريبية:', error);
    res.status(500).json({ error: 'فشل في جلب الدورات التدريبية' });
  }
});

// إضافة دورة تدريبية جديدة
router.post('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);
    
    const {
      employee_code,
      employee_name,
      department,
      course_name,
      course_date,
      course_duration,
      training_type,
      course_cost,
      notes
    } = req.body;

    // التحقق من صحة البيانات
    if (!employee_code || !employee_name || !department || !course_name || !course_date || !course_duration || !training_type) {
      return res.status(400).json({ error: 'يرجى ملء جميع الحقول المطلوبة' });
    }

    // التحقق من حالة الموظف
    const [employeeCheck] = await pool.promise().query(
      "SELECT status FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeCheck.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    if (employeeCheck[0].status === 'مستقيل') {
      return res.status(400).json({ error: 'لا يمكن إضافة دورة تدريبية لموظف مستقيل' });
    }

    const sql = `INSERT INTO training_courses 
      (employee_code, employee_name, department, course_name, course_date, course_duration, training_type, course_cost, notes) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    const [result] = await pool.promise().query(sql, [
      employee_code,
      employee_name,
      department,
      course_name,
      course_date,
      parseInt(course_duration),
      training_type,
      parseFloat(course_cost) || 0,
      notes || null
    ]);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'training',
      record_id: result.insertId.toString(),
      message: `تم إضافة دورة تدريبية: ${course_name} للموظف: ${employee_name} (كود: ${employee_code}) بتاريخ: ${course_date} لمدة: ${course_duration} ساعة`
    });

    res.status(201).json({
      message: 'تمت إضافة الدورة التدريبية بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة الدورة التدريبية:', error);
    res.status(500).json({ error: 'فشل في إضافة الدورة التدريبية' });
  }
});

// تحديث دورة تدريبية
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { id } = req.params;
    const {
      employee_code,
      employee_name,
      department,
      course_name,
      course_date,
      course_duration,
      training_type,
      course_cost,
      notes
    } = req.body;

    // التحقق من صحة البيانات
    if (!employee_code || !employee_name || !department || !course_name || !course_date || !course_duration || !training_type) {
      return res.status(400).json({ error: 'يرجى ملء جميع الحقول المطلوبة' });
    }

    // إعداد البيانات للتحديث
    const updateData = {
      employee_code,
      employee_name,
      department,
      course_name,
      course_date,
      course_duration: parseInt(course_duration),
      training_type,
      course_cost: parseFloat(course_cost) || 0,
      notes: notes || null
    };

    // تنظيف البيانات ومعالجة حقول التاريخ
    const cleanedData = cleanUpdateData(updateData);

    // تحضير استعلام التحديث
    const { setClause, values } = prepareUpdateQuery(cleanedData);

    const [result] = await pool.promise().query(
      `UPDATE training_courses SET ${setClause} WHERE id = ?`,
      [...values, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'الدورة التدريبية غير موجودة' });
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'training',
      record_id: id.toString(),
      message: `تم تعديل دورة تدريبية: ${course_name} للموظف: ${employee_name} (كود: ${employee_code}) بتاريخ: ${course_date} لمدة: ${course_duration} ساعة`
    });

    res.json({ message: 'تم تحديث الدورة التدريبية بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الدورة التدريبية:', error);
    res.status(500).json({ error: 'فشل في تحديث الدورة التدريبية' });
  }
});

// حذف دورة تدريبية
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { id } = req.params;

    // الحصول على معلومات الدورة قبل الحذف
    const [trainingData] = await pool.promise().query(
      'SELECT employee_code, employee_name, course_name, course_date, course_duration FROM training_courses WHERE id = ?',
      [id]
    );

    if (trainingData.length === 0) {
      return res.status(404).json({ error: 'الدورة التدريبية غير موجودة' });
    }

    const training = trainingData[0];

    // حذف الدورة
    const [result] = await pool.promise().query(
      'DELETE FROM training_courses WHERE id = ?',
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'training',
      record_id: id.toString(),
      message: `تم حذف دورة تدريبية: ${training.course_name} للموظف: ${training.employee_name} (كود: ${training.employee_code}) - التاريخ: ${training.course_date} - المدة: ${training.course_duration} ساعة`
    });

    res.json({ message: 'تم حذف الدورة التدريبية بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الدورة التدريبية:', error);
    res.status(500).json({ error: 'فشل في حذف الدورة التدريبية' });
  }
});

// البحث في الدورات التدريبية
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);
    
    const { employee_code, employee_name, course_name, training_type, department, start_date, end_date } = req.query;
    
    let query = "SELECT * FROM training_courses WHERE 1=1";
    const params = [];
    
    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }
    
    if (employee_name) {
      query += " AND employee_name LIKE ?";
      params.push(`%${employee_name}%`);
    }
    
    if (course_name) {
      query += " AND course_name LIKE ?";
      params.push(`%${course_name}%`);
    }
    
    if (training_type) {
      query += " AND training_type = ?";
      params.push(training_type);
    }
    
    if (department) {
      query += " AND department = ?";
      params.push(department);
    }
    
    if (start_date) {
      query += " AND course_date >= ?";
      params.push(start_date);
    }
    
    if (end_date) {
      query += " AND course_date <= ?";
      params.push(end_date);
    }
    
    query += " ORDER BY created_at DESC, id DESC";
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث عن الدورات التدريبية:', error);
    res.status(500).json({ error: 'فشل في البحث عن الدورات التدريبية' });
  }
});

// إحصائيات التدريب
router.get('/statistics', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createTrainingTable(pool);
    
    const { department, training_type, start_date, end_date } = req.query;
    
    let query = "SELECT COUNT(*) as total_courses, SUM(course_cost) as total_cost, AVG(course_duration) as avg_duration FROM training_courses WHERE 1=1";
    const params = [];
    
    if (department) {
      query += " AND department = ?";
      params.push(department);
    }
    
    if (training_type) {
      query += " AND training_type = ?";
      params.push(training_type);
    }
    
    if (start_date) {
      query += " AND course_date >= ?";
      params.push(start_date);
    }
    
    if (end_date) {
      query += " AND course_date <= ?";
      params.push(end_date);
    }
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب إحصائيات التدريب:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات التدريب' });
  }
});

module.exports = router;
