/* ملف CSS موحد للتنسيقات المشتركة بين جميع الأقسام */

/* ===== متغيرات CSS للألوان والقيم المشتركة ===== */
:root {
  /* الألوان الأساسية */
  --primary-color: #1976D2;
  --primary-hover: #0b7dda;
  --primary-light: #e3f2fd;
  --primary-dark: #0d47a1;

  /* ألوان الحالات */
  --success-color: #4CAF50;
  --success-hover: #45a049;
  --danger-color: #f44336;
  --danger-hover: #d32f2f;
  --warning-color: #ff9800;
  --warning-hover: #f57c00;
  --info-color: #2196F3;
  --info-hover: #0b7dda;

  /* ألوان النصوص */
  --text-primary: #333;
  --text-secondary: #666;
  --text-light: #999;
  --text-white: #fff;

  /* ألوان الخلفيات */
  --bg-white: #fff;
  --bg-light: #f9f9f9;
  --bg-gray: #f0f0f0;
  --bg-dark: #e0e0e0;

  /* ألوان الحدود */
  --border-color: #ddd;
  --border-light: #e0e0e0;
  --border-focus: var(--primary-color);

  /* الظلال */
  --shadow-light: 0 2px 4px rgba(0,0,0,0.05);
  --shadow-medium: 0 2px 5px rgba(0,0,0,0.1);
  --shadow-heavy: 0 3px 10px rgba(0,0,0,0.1);
  --shadow-focus: 0 0 8px rgba(33, 150, 243, 0.4);

  /* المسافات */
  --spacing-xs: 5px;
  --spacing-sm: 10px;
  --spacing-md: 15px;
  --spacing-lg: 20px;
  --spacing-xl: 25px;

  /* أحجام الخطوط */
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;

  /* نصف أقطار الحدود */
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;

  /* الانتقالات - تم إزالتها لتحسين الأداء */
}

/* ===== تنسيقات التبويبات المشتركة ===== */
.tabs {
  display: flex;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  border-bottom: 2px solid var(--border-light);
  padding-bottom: var(--spacing-xs);
}

.tab-btn {
  padding: 12px var(--spacing-xl);
  background-color: var(--bg-gray);
  border: 1px solid var(--border-color);
  border-bottom: none;
  cursor: pointer;
  margin-right: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 0;
  font-weight: normal;
  color: var(--text-primary);

  position: relative;
  overflow: hidden;
  font-size: var(--font-size-base);
}

.tab-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
  transform: scaleX(0);

}

.tab-btn:hover {
  background-color: var(--bg-dark);
  transform: translateY(-1px);
}

.tab-btn:hover::after {
  transform: scaleX(1);
}

.tab-btn:active {
  transform: scale(0.97);
}

.tab-btn.active {
  background-color: var(--primary-color);
  color: var(--text-white);
  border-bottom: 2px solid var(--primary-color);
  font-weight: bold;
}

.tab-btn.active::after {
  transform: scaleX(1);
  background-color: var(--text-white);
}

.tab-content {
  display: none;
  border: 1px solid var(--border-color);
  padding: var(--spacing-xl);
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) var(--border-radius-lg);
  background-color: var(--bg-white);
  box-shadow: var(--shadow-heavy);
}

.tab-content.active {
  display: block;
}

/* ===== تنسيقات النماذج المشتركة ===== */
.form-group {
  margin-bottom: var(--spacing-lg);
  position: relative;
}

/* ===== تنسيقات خاصة لحقول التاريخ العربية ===== */
input[type="date"] {
  /* تعيين الاتجاه للعربية */
  direction: rtl;
  text-align: right;

  /* التصميم العصري */
  background: #fafbfc;
  border: 1.5px solid #e1e8ed;
  border-radius: 12px;
  padding: 14px 16px;
  font-size: 15px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #2c3e50;

  /* تأثيرات بصرية */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* إزالة المظهر الافتراضي */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  /* تحسين العرض */
  min-height: 48px;
  width: 100%;
  box-sizing: border-box;
}

/* حالة التركيز */
input[type="date"]:focus {
  outline: none;
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.12),
              0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* حالة التمرير */
input[type="date"]:hover:not(:focus) {
  border-color: #c1c9d2;
  background: #ffffff;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
}

/* تنسيق أيقونة التقويم */
input[type="date"]::-webkit-calendar-picker-indicator {
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: 18px 18px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  margin-left: 8px;
  margin-right: 0;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* تنسيق للمتصفحات الأخرى */
input[type="date"]::-moz-calendar-picker-indicator {
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E") no-repeat center;
  background-size: 18px 18px;
  width: 20px;
  height: 20px;
  cursor: pointer;
}

/* تنسيق النص المدخل */
input[type="date"]::-webkit-datetime-edit {
  direction: rtl;
  text-align: right;
  padding: 0;
}

input[type="date"]::-webkit-datetime-edit-fields-wrapper {
  direction: rtl;
}

input[type="date"]::-webkit-datetime-edit-text {
  color: #6c757d;
  padding: 0 2px;
}

input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-day-field,
input[type="date"]::-webkit-datetime-edit-year-field {
  color: #2c3e50;
  font-weight: 500;
}

/* حالة عدم التوفر */
input[type="date"]:disabled {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

input[type="date"]:disabled::-webkit-calendar-picker-indicator {
  opacity: 0.4;
  cursor: not-allowed;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  input[type="date"] {
    font-size: 16px; /* لمنع التكبير في iOS */
    padding: 12px 14px;
    min-height: 44px;
  }

  input[type="date"]::-webkit-calendar-picker-indicator {
    background-size: 16px 16px;
    width: 18px;
    height: 18px;
  }
}

/* تنسيقات خاصة لقسم التدريب */
.training-form {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: var(--bg-light);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
}

/* تنسيقات خاصة لقسم التقييم */
.evaluation-form {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: var(--bg-light);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
}

.training-form .form-group.full-width,
.evaluation-form .form-group.full-width {
  grid-column: 1 / -1;
}

.training-form .form-actions,
.evaluation-form .form-actions {
  grid-column: 1 / -1;
}

/* تنسيقات الفلاتر الموحدة */
.unified-filters {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: var(--bg-light);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
}

.unified-filters .filter-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);
}

.unified-filters .form-group {
  display: flex;
  flex-direction: column;
}

.unified-filters label {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
  font-weight: 600;
}

.unified-filters input,
.unified-filters select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-white);
  transition: border-color 0.3s ease;
}

.unified-filters input:focus,
.unified-filters select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.unified-filters .filter-actions {
  margin-top: var(--spacing-md);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

/* تنسيقات بطاقات التقارير */
.reports-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-md) 0;
}

/* تأثيرات الحركة للبطاقات */
.report-card {
  animation: fadeInUp 0.6s ease-out;
}

.report-card:nth-child(1) {
  animation-delay: 0.1s;
}

.report-card:nth-child(2) {
  animation-delay: 0.2s;
}

.report-card:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تأثير النبض للقيم */
.report-value {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.report-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.report-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff);
  border-radius: 16px 16px 0 0;
}

.report-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-8px) scale(1.02);
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
}

.report-card:hover::before {
  background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff);
  height: 5px;
}

.report-card:hover .report-icon {
  transform: scale(1.1);
  color: #ffffff;
}

.report-card:hover .report-value {
  color: #ffffff;
}

.report-icon {
  font-size: 40px;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-md);
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.2));
}

.report-value {
  font-size: 36px;
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-sm);
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Arial', sans-serif;
  letter-spacing: -0.5px;
}

.report-footer {
  font-size: 15px;
  color: #64748b;
  font-weight: 600;
  padding: 0 var(--spacing-sm);
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
  transition: all 0.3s ease;
}

/* تنسيقات خاصة لبطاقات الاستقالات */
#total-resignations-report::before {
  background: linear-gradient(90deg, #ef4444, #f97316);
}

#total-resignations-report .report-icon {
  background: linear-gradient(135deg, #ef4444, #f97316);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(239, 68, 68, 0.3));
}

#total-resignations-report:hover .report-icon {
  color: #dc2626;
}

#recommended-resignations-report::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

#recommended-resignations-report .report-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3));
}

#recommended-resignations-report:hover .report-icon {
  color: #059669;
}

#not-recommended-resignations-report::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

#not-recommended-resignations-report .report-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3));
}

#not-recommended-resignations-report:hover .report-icon {
  color: #d97706;
}

/* تنسيقات جداول التقارير */
.reports-tables {
  background: var(--bg-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-xl);
  border: 1px solid var(--border-light);
}

.search-hint {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  display: block;
}

.training-table-container {
  margin-top: var(--spacing-xl);
}

.training-table-container h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: var(--spacing-sm);
}

.reports-section {
  padding: var(--spacing-lg);
}

.reports-section h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-xl);
  text-align: center;
  border-bottom: 3px solid var(--primary-color);
  padding-bottom: var(--spacing-md);
}

.report-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: var(--bg-light);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.filter-group input,
.filter-group select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  transition: border-color 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.summary-card {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--text-white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
  box-shadow: var(--shadow-heavy);
  transition: transform 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-5px);
}

.summary-card h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.summary-card span {
  font-size: 24px;
  font-weight: bold;
}

.generate-btn {
  background: linear-gradient(135deg, var(--warning-color), var(--warning-hover));
  color: var(--text-white);
}

/* تنسيقات قائمة الاقتراحات */
.suggestions-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-heavy);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  border-bottom: 1px solid var(--border-light);
  transition: background-color 0.2s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background-color: var(--bg-light);
}

.suggestion-item strong {
  color: var(--primary-color);
  font-weight: 600;
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: bold;
  color: var(--text-primary);

  position: relative;
  font-size: var(--font-size-base);
}

.form-group:hover label {
  color: var(--primary-color);
}

.form-group select,
.form-group input:not([type="date"]),
.form-group textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);

  background-color: var(--bg-light);
  box-sizing: border-box;
}

/* تنسيقات خاصة لحقول التاريخ داخل form-group */
.form-group input[type="date"] {
  /* الحفاظ على التنسيقات المخصصة للتاريخ */
  width: 100%;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: var(--shadow-focus);
  background-color: var(--bg-white);
  transform: translateY(-2px);
}

.form-group input[readonly],
.form-group select[readonly],
.form-group textarea[readonly] {
  background-color: var(--bg-gray);
  color: var(--text-secondary);
  cursor: not-allowed;
  border-color: var(--border-color);
  box-shadow: none;
  border-style: dashed;
  transform: none;
}

/* تنسيقات إضافية للنماذج */
.form-row {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

/* ===== تنسيقات الأزرار المشتركة ===== */
/* فئة .btn الأساسية - ترث نفس التنسيقات */
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none !important;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-base);
  font-weight: bold;
  cursor: pointer;

  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-width: 100px;
  text-decoration: none;
  box-sizing: border-box;
  outline: none !important;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn:focus {
  outline: none !important;
  border: none !important;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

/* جميع الأزرار ترث التنسيق الأساسي */
.save-btn,
.reset-btn,
.export-btn,
.print-btn,
.search-btn,
.show-all-btn,
.clear-all-btn,
.edit-btn,
.delete-btn,
.details-btn,
.view-btn,
.view-employee-btn,
.btn-primary,
.btn-success,
.btn-danger,
.btn-warning,
.btn-info,
.btn-secondary {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-base);
  font-weight: bold;
  cursor: pointer;

  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-width: 100px;
  text-decoration: none;
  box-sizing: border-box;
}

/* تم إزالة تأثيرات hover لتحسين الأداء */

/* تم إزالة تأثيرات active لتحسين الأداء */

/* الألوان المختلفة للأزرار */
/* أزرار الحفظ والنجاح */
.save-btn, .btn-success {
  background-color: var(--success-color);
  color: var(--text-white);
}

.save-btn:hover, .btn-success:hover {
  background-color: var(--success-hover);
}

/* أزرار الإعادة تعيين والخطر */
.reset-btn, .btn-danger {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.reset-btn:hover, .btn-danger:hover {
  background-color: var(--danger-hover);
}

/* الأزرار الأساسية */
.export-btn, .print-btn, .search-btn, .show-all-btn, .clear-all-btn, .btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.export-btn:hover, .print-btn:hover, .search-btn:hover, .show-all-btn:hover, .clear-all-btn:hover, .btn-primary:hover {
  background-color: var(--primary-hover);
}

/* أزرار التحذير */
.btn-warning {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-warning:hover {
  background-color: var(--warning-hover);
}

/* أزرار المعلومات */
.btn-info {
  background-color: var(--info-color);
  color: var(--text-white);
}

.btn-info:hover {
  background-color: var(--info-hover);
}

/* أزرار ثانوية */
.btn-secondary {
  background-color: var(--text-secondary);
  color: var(--text-white);
}

.btn-secondary:hover {
  background-color: var(--text-primary);
}

/* أزرار الإجراءات الخاصة */
.edit-btn {
  background-color: #007bff;
  color: var(--text-white);
}

.edit-btn:hover {
  background-color: #0056b3;
}

.delete-btn {
  background-color: #dc3545;
  color: var(--text-white);
}

.delete-btn:hover {
  background-color: #c82333;
}

.details-btn {
  background-color: #28a745;
  color: var(--text-white);
}

.details-btn:hover {
  background-color: #218838;
}

.view-employee-btn {
  background-color: #6f42c1;
  color: var(--text-white);
}

.view-employee-btn:hover {
  background-color: #5a2d91;
}

.view-btn {
  background-color: #186F65;
  color: var(--text-white);
}

.view-btn:hover {
  background-color: #0F4C45;
}

/* أزرار الخطوط - نفس التنسيق مع حدود */
.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-base);
  font-weight: bold;
  cursor: pointer;

  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  min-width: 100px;
  text-decoration: none;
  box-sizing: border-box;
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-outline:active {
  transform: scale(0.95);
}

/* أحجام الأزرار - تطبق على جميع الأزرار */
.btn-sm,
.save-btn.btn-sm,
.reset-btn.btn-sm,
.export-btn.btn-sm,
.print-btn.btn-sm,
.search-btn.btn-sm,
.show-all-btn.btn-sm,
.clear-all-btn.btn-sm,
.edit-btn.btn-sm,
.delete-btn.btn-sm,
.details-btn.btn-sm,
.view-employee-btn.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-width: 80px;
}

.btn-lg,
.save-btn.btn-lg,
.reset-btn.btn-lg,
.export-btn.btn-lg,
.print-btn.btn-lg,
.search-btn.btn-lg,
.show-all-btn.btn-lg,
.clear-all-btn.btn-lg,
.edit-btn.btn-lg,
.delete-btn.btn-lg,
.details-btn.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-width: 120px;
}

/* تم دمج تنسيقات أزرار الإجراءات مع التنسيقات الموحدة أعلاه */

/* تنسيقات خاصة للأيقونات في الأزرار */
.edit-btn i,
.delete-btn i,
.details-btn i,
.export-btn i,
.print-btn i,
.search-btn i,
.save-btn i,
.reset-btn i {
  pointer-events: none; /* منع الأيقونة من التداخل مع النقر */
}

/* تنسيقات خاصة لأزرار الإجراءات في الجداول */
.edit-btn,
.delete-btn,
.details-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-width: 60px;
  margin-right: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

/* تنسيقات أزرار الإجراءات داخل خلايا الجداول */
table td .edit-btn,
table td .delete-btn,
table td .details-btn,
.custody-table td .edit-btn,
.custody-table td .delete-btn,
.custody-table td .details-btn,
.delivery-table td .edit-btn,
.delivery-table td .delete-btn,
.delivery-table td .details-btn,
.return-table td .edit-btn,
.return-table td .delete-btn,
.return-table td .details-btn,
.evaluation-table td .edit-btn,
.evaluation-table td .delete-btn,
.evaluation-table td .details-btn,
.employee-table td .edit-btn,
.employee-table td .delete-btn,
.employee-table td .details-btn,
.data-table td .edit-btn,
.data-table td .delete-btn,
.data-table td .details-btn {
  padding: 3px 6px !important;
  font-size: 11px !important;
  min-width: 45px !important;
  margin: 1px !important;
  border-radius: 3px !important;
  line-height: 1.2 !important;
}

/* تنسيقات خاصة لرؤوس خانة الإجراءات */
.actions-cell,
th.actions-column,
th:last-child {
  width: 180px;
  min-width: 180px;
  max-width: 220px;
  white-space: nowrap;
  text-align: center;
  padding: 8px 5px;
  background-color: var(--primary-color);
  color: var(--text-white);
  font-weight: bold;
}

/* تنسيقات خاصة لخلايا خانة الإجراءات */
td.actions-column,
td:last-child {
  width: 180px;
  min-width: 180px;
  max-width: 220px;
  white-space: nowrap;
  text-align: center;
  padding: 8px 5px;
  background-color: rgba(33, 150, 243, 0.05);
}

/* تطبيق تلقائي على خانات الإجراءات */
th:contains("الإجراءات"),
th[data-column="actions"],
td:has(.edit-btn),
td:has(.delete-btn),
td:has(.details-btn) {
  width: 120px;
  min-width: 120px;
  max-width: 150px;
  white-space: nowrap;
  text-align: center;
  padding: 8px 5px;
}

/* تحسين تنسيق الأزرار في خانة الإجراءات - تطبيق قوي */
.actions-cell .edit-btn,
.actions-cell .delete-btn,
.actions-cell .details-btn,
.actions-column .edit-btn,
.actions-column .delete-btn,
.actions-column .details-btn,
table .edit-btn,
table .delete-btn,
table .details-btn,
table .action-btn {
  padding: 6px 10px !important;
  font-size: 12px !important;
  min-width: 60px !important;
  height: 32px !important;
  margin: 2px !important;
  border-radius: 4px !important;
  line-height: 1.3 !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
}

/* عرض الأزرار فقط إذا لم تكن مخفية بواسطة الصلاحيات */
.actions-cell .edit-btn:not([style*="display: none"]),
.actions-cell .delete-btn:not([style*="display: none"]),
.actions-cell .details-btn:not([style*="display: none"]),
.actions-column .edit-btn:not([style*="display: none"]),
.actions-column .delete-btn:not([style*="display: none"]),
.actions-column .details-btn:not([style*="display: none"]),
table .edit-btn:not([style*="display: none"]),
table .delete-btn:not([style*="display: none"]),
table .details-btn:not([style*="display: none"]),
table .action-btn:not([style*="display: none"]) {
  display: inline-flex !important;
}

/* تنسيقات إضافية لضمان التطبيق */
[class*="table"] .edit-btn,
[class*="table"] .delete-btn,
[class*="table"] .details-btn {
  padding: 3px 6px !important;
  font-size: 11px !important;
  min-width: 45px !important;
  margin: 1px !important;
  border-radius: 3px !important;
}

/* تحديد عرض خانة الإجراءات تلقائياً */
/* تنسيقات رؤوس خانة الإجراءات */
table th:nth-last-child(1),
.custody-table th:nth-last-child(1),
.delivery-table th:nth-last-child(1),
.return-table th:nth-last-child(1),
.evaluation-table th:nth-last-child(1),
.employee-table th:nth-last-child(1),
.data-table th:nth-last-child(1),
.vacations-table th:nth-last-child(1),
.contribution-table th:nth-last-child(1),
.rewards-table th:nth-last-child(1),
.deductions-table th:nth-last-child(1) {
  width: 180px !important;
  min-width: 180px !important;
  max-width: 220px !important;
  white-space: nowrap !important;
  text-align: center !important;
  padding: 8px 5px !important;
  background-color: var(--primary-color) !important;
  color: var(--text-white) !important;
  font-weight: bold !important;
}

/* تنسيقات خلايا خانة الإجراءات */
table td:nth-last-child(1),
.custody-table td:nth-last-child(1),
.delivery-table td:nth-last-child(1),
.return-table td:nth-last-child(1),
.evaluation-table td:nth-last-child(1),
.employee-table td:nth-last-child(1),
.data-table td:nth-last-child(1),
.vacations-table td:nth-last-child(1),
.contribution-table td:nth-last-child(1),
.rewards-table td:nth-last-child(1),
.deductions-table td:nth-last-child(1) {
  width: 180px !important;
  min-width: 180px !important;
  max-width: 220px !important;
  white-space: nowrap !important;
  text-align: center !important;
  padding: 8px 5px !important;
  background-color: rgba(33, 150, 243, 0.05) !important;
}

/* تنسيقات قوية جداً لضمان التطبيق */
table thead tr th:last-child {
  width: 180px !important;
  min-width: 180px !important;
  max-width: 220px !important;
  background-color: var(--primary-color) !important;
  color: var(--text-white) !important;
  font-weight: bold !important;
}

/* تنسيق قوي جداً لضمان اللون الأزرق لجميع رؤوس الجداول */
th,
table th,
.table th,
.employee-table th,
.data-table th,
.users-table th,
.report-table th,
#previewTable th {
  background-color: var(--primary-color) !important;
  color: var(--text-white) !important;
}

/* تنسيق موحد لجميع أزرار التصدير */
.export-btn {
  background-color: var(--primary-color) !important;
  color: #ffffff !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  font-size: 1rem !important;
  font-weight: 600 !important;

}

.export-btn:hover {
  background-color: var(--primary-dark) !important;
}

/* تنسيق موحد للنوافذ المنبثقة (المودال) */
.modal,
.modal-overlay {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: #ffffff !important;
  margin: 5% auto;
  padding: 0;
  border-radius: 12px;
  width: 85%;
  max-width: 800px; /* زيادة العرض لاستيعاب 3 حقول */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff !important;
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
  background-color: #ffffff !important;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background-color: #ffffff !important;
}

.close,
.modal-close {
  position: absolute;
  top: 15px;
  left: 15px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  color: #999;
  transition: color 0.3s;
  background: none;
  border: none;
  padding: 5px 10px;
  border-radius: 50%;
}

.close:hover,
.modal-close:hover {
  color: #333;
  background-color: rgba(0, 0, 0, 0.1);
}

/* تنسيق الصفوف والحقول في النوافذ المنبثقة */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-end;
}

.form-row .form-group {
  flex: 1;
  min-width: 0; /* للسماح بتقليص الحقول عند الحاجة */
}

.form-row .form-group.full-width {
  flex: 3; /* لجعل حقل الملاحظات يأخذ العرض الكامل */
}

.form-row .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.form-row .form-group input:not([type="date"]),
.form-row .form-group select,
.form-row .form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  font-family: inherit;
}

/* تنسيقات خاصة لحقول التاريخ في form-row */
.form-row .form-group input[type="date"] {
  width: 100%;
  box-sizing: border-box;
  font-family: inherit;
}

.form-row .form-group input:focus,
.form-row .form-group select:focus,
.form-row .form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-row .form-group input[readonly] {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  border-color: #dee2e6;
}

.form-row .form-group textarea {
  resize: vertical;
  min-height: 80px;
  line-height: 1.5;
}

.form-row .form-group select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-left: 40px;
}

/* تحسين تنسيق الحقول الفارغة */
.form-row .form-group:empty {
  flex: 1;
  min-height: 1px;
}

/* تحسينات إضافية للنوافذ المنبثقة */
.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.modal-footer .save-btn,
.modal-footer .confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-footer .save-btn:hover,
.modal-footer .confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.modal-footer .cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-footer .cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* تنسيق متجاوب للشاشات الصغيرة */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 2% auto;
    max-width: none;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .form-row .form-group {
    flex: none;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 10px;
  }

  .modal-footer button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    margin: 1% auto;
  }

  .modal-body {
    padding: 15px;
  }

  .form-row .form-group input,
  .form-row .form-group select,
  .form-row .form-group textarea {
    padding: 10px;
    font-size: 16px; /* لمنع التكبير في iOS */
  }
}

/* تنسيق التحقق من صحة البيانات */
.error-field {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
  background-color: #fff5f5 !important;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
  animation: errorSlideIn 0.3s ease-out;
}

.error-message::before {
  content: "⚠️";
  font-size: 14px;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-field {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
}

.success-message {
  color: #28a745;
  font-size: 12px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
  animation: successSlideIn 0.3s ease-out;
}

.success-message::before {
  content: "✅";
  font-size: 14px;
}

@keyframes successSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين تنسيق رسائل التحقق في النوافذ المنبثقة */
.modal-body .error-message,
.modal-body .success-message {
  margin-bottom: 10px;
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: 500;
}

.modal-body .error-message {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.modal-body .success-message {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

/* تنسيق الإشعارات المحسنة */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
  pointer-events: none;
}

.notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  pointer-events: auto;
  border-left: 4px solid #007bff;
}

.notification-show {
  opacity: 1;
  transform: translateX(0);
}

.notification-hide {
  opacity: 0;
  transform: translateX(100%);
}

.notification-success {
  border-left-color: #28a745;
}

.notification-error {
  border-left-color: #dc3545;
}

.notification-warning {
  border-left-color: #ffc107;
}

.notification-info {
  border-left-color: #17a2b8;
}

.notification-loading {
  border-left-color: #6f42c1;
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.notification-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: #2c3e50;
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.notification-close:hover {
  background-color: #f8f9fa;
  color: #495057;
}

/* تنسيق أزرار التحميل */
.button-loading {
  position: relative;
  color: transparent !important;
}

.button-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* تنسيق حوار التأكيد */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.confirm-dialog-show {
  opacity: 1;
}

.confirm-dialog-hide {
  opacity: 0;
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.confirm-dialog-show .confirm-dialog {
  transform: scale(1);
}

.confirm-dialog-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 12px;
}

.confirm-dialog-icon {
  font-size: 24px;
}

.confirm-dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
}

.confirm-dialog-body {
  padding: 20px 24px;
}

.confirm-dialog-body p {
  margin: 0;
  line-height: 1.5;
  color: #495057;
}

.confirm-dialog-footer {
  padding: 16px 24px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* تنسيق مؤشر التحميل في النوافذ المنبثقة */
.modal-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  border-radius: 12px;
}

.modal-loading-content {
  text-align: center;
  color: #495057;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-message {
  font-size: 16px;
  font-weight: 500;
}

/* تنسيق العناصر المعطلة بسبب الصلاحيات */
.no-permission {
  display: none !important;
}

button.no-permission,
input.no-permission,
select.no-permission {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

table tbody tr td:last-child {
  width: 180px !important;
  min-width: 180px !important;
  max-width: 220px !important;
  background-color: rgba(33, 150, 243, 0.05) !important;
}

/* تنسيقات الأزرار في جميع الجداول - أحجام محسنة */
table button.edit-btn,
table button.delete-btn,
table button.details-btn,
table button.view-btn,
table button.view-employee-btn,
table button.action-btn {
  padding: 6px 10px !important;
  font-size: 12px !important;
  min-width: 60px !important;
  height: 32px !important;
  margin: 2px !important;
  border-radius: 4px !important;
  line-height: 1.3 !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
}

/* عرض الأزرار فقط إذا لم تكن مخفية بواسطة الصلاحيات */
table button.edit-btn:not([style*="display: none"]),
table button.delete-btn:not([style*="display: none"]),
table button.details-btn:not([style*="display: none"]),
table button.view-btn:not([style*="display: none"]),
table button.view-employee-btn:not([style*="display: none"]),
table button.action-btn:not([style*="display: none"]) {
  display: inline-flex !important;
}

/* تنسيقات للأزرار بأيقونات فقط في الجداول */
.actions-cell .edit-btn i,
.actions-cell .delete-btn i,
.actions-cell .details-btn i,
.actions-column .edit-btn i,
.actions-column .delete-btn i,
.actions-column .details-btn i {
  font-size: 10px;
  margin-right: 2px;
}

/* تنسيقات متجاوبة لخانة الإجراءات */
@media (max-width: 768px) {
  .actions-cell,
  .actions-column,
  table th:nth-last-child(1),
  table td:nth-last-child(1) {
    width: 150px !important;
    min-width: 150px !important;
    max-width: 180px !important;
    padding: 5px 2px;
  }

  .actions-cell .edit-btn:not([style*="display: none"]),
  .actions-cell .delete-btn:not([style*="display: none"]),
  .actions-cell .details-btn:not([style*="display: none"]),
  .actions-column .edit-btn:not([style*="display: none"]),
  .actions-column .delete-btn:not([style*="display: none"]),
  .actions-column .details-btn:not([style*="display: none"]),
  table .edit-btn:not([style*="display: none"]),
  table .delete-btn:not([style*="display: none"]),
  table .details-btn:not([style*="display: none"]),
  table .action-btn:not([style*="display: none"]) {
    padding: 4px 6px !important;
    font-size: 11px !important;
    min-width: 50px !important;
    height: 28px !important;
    margin: 1px !important;
    display: inline-flex !important;
  }

  .actions-cell .edit-btn i,
  .actions-cell .delete-btn i,
  .actions-cell .details-btn i,
  .actions-column .edit-btn i,
  .actions-column .delete-btn i,
  .actions-column .details-btn i {
    font-size: 10px;
    margin-right: 1px;
  }
}

/* تأكيد عدم وجود حدود أو استروك */
.save-btn,
.reset-btn,
.export-btn,
.print-btn,
.search-btn,
.show-all-btn,
.clear-all-btn,
.edit-btn,
.delete-btn,
.details-btn,
.view-employee-btn,
.btn-primary,
.btn-success,
.btn-danger,
.btn-warning,
.btn-info,
.btn-secondary {
  border: none !important;
  outline: none !important;
  box-shadow: none;
}

.save-btn:focus,
.reset-btn:focus,
.export-btn:focus,
.print-btn:focus,
.search-btn:focus,
.show-all-btn:focus,
.clear-all-btn:focus,
.edit-btn:focus,
.delete-btn:focus,
.details-btn:focus,
.view-employee-btn:focus,
.btn-primary:focus,
.btn-success:focus,
.btn-danger:focus,
.btn-warning:focus,
.btn-info:focus,
.btn-secondary:focus {
  outline: none !important;
  border: none !important;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

/* ===== تنسيقات الجداول المشتركة ===== */
.table, table, .custody-table, .evaluation-table, .contribution-table, .rewards-table, .deductions-table, .vacation-table, .report-table, .delivery-table, .undelivered-table, .return-table, .employee-table, .vacations-table, .added-vacations-table, .vacations-details-table, .custody-recipients-table, .data-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: var(--spacing-sm);
  box-shadow: var(--shadow-medium);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background-color: var(--bg-white);
  font-size: var(--font-size-base);
}

.table thead th, th, .custody-table th, .evaluation-table th, .contribution-table th, .rewards-table th, .deductions-table th, .vacation-table th, .report-table th, .delivery-table th, .undelivered-table th, .return-table th, .employee-table th, .vacations-table th, .added-vacations-table th, .vacations-details-table th, .custody-recipients-table th, .data-table th {
  padding: 12px var(--spacing-md);
  text-align: right;
  border-bottom: 1px solid var(--primary-dark);
  border-right: 1px solid var(--primary-dark);
  background-color: var(--primary-color);
  font-weight: bold;
  color: var(--text-white);
  position: sticky;
  top: 0;
  z-index: 10;
  font-size: var(--font-size-base);
}

/* إزالة الحدود من رؤوس الخلايا الأخيرة */
.table thead th:last-child,
.custody-table th:last-child,
.evaluation-table th:last-child,
.contribution-table th:last-child,
.rewards-table th:last-child,
.deductions-table th:last-child,
.vacation-table th:last-child,
.report-table th:last-child,
.delivery-table th:last-child,
.undelivered-table th:last-child,
.return-table th:last-child,
.employee-table th:last-child,
.vacations-table th:last-child,
.added-vacations-table th:last-child,
.vacations-details-table th:last-child,
.custody-recipients-table th:last-child,
.data-table th:last-child {
  border-right: none;
}

.table tbody td, td, .custody-table td, .evaluation-table td, .contribution-table td, .rewards-table td, .deductions-table td, .vacation-table td, .report-table td, .delivery-table td, .undelivered-table td, .return-table td, .employee-table td, .vacations-table td, .added-vacations-table td, .vacations-details-table td, .custody-recipients-table td, .data-table td {
  padding: 12px var(--spacing-md);
  text-align: right;
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);

  vertical-align: middle;
  font-size: var(--font-size-base);
}

/* إزالة الحدود من الخلايا الأخيرة */
.table tbody td:last-child,
.custody-table td:last-child,
.evaluation-table td:last-child,
.contribution-table td:last-child,
.rewards-table td:last-child,
.deductions-table td:last-child,
.vacation-table td:last-child,
.report-table td:last-child,
.delivery-table td:last-child,
.undelivered-table td:last-child,
.return-table td:last-child,
.employee-table td:last-child,
.vacations-table td:last-child,
.added-vacations-table td:last-child,
.vacations-details-table td:last-child,
.custody-recipients-table td:last-child,
.data-table td:last-child {
  border-right: none;
}

/* إزالة الحدود من الصف الأخير */
.table tbody tr:last-child td,
.custody-table tr:last-child td,
.evaluation-table tr:last-child td,
.contribution-table tr:last-child td,
.rewards-table tr:last-child td,
.deductions-table tr:last-child td,
.vacation-table tr:last-child td,
.report-table tr:last-child td,
.delivery-table tr:last-child td,
.undelivered-table tr:last-child td,
.return-table tr:last-child td,
.employee-table tr:last-child td,
.vacations-table tr:last-child td,
.added-vacations-table tr:last-child td,
.vacations-details-table tr:last-child td,
.custody-recipients-table tr:last-child td,
.data-table tr:last-child td {
  border-bottom: none;
}

.table tbody tr:hover, tr:hover, .custody-table tr:hover, .evaluation-table tr:hover, .contribution-table tr:hover, .rewards-table tr:hover, .deductions-table tr:hover, .vacation-table tr:hover, .report-table tr:hover, .delivery-table tr:hover, .undelivered-table tr:hover, .return-table tr:hover, .employee-table tr:hover, .vacations-table tr:hover, .added-vacations-table tr:hover, .vacations-details-table tr:hover, .custody-recipients-table tr:hover, .data-table tr:hover {
  background-color: var(--primary-light);
}

.table tbody tr:nth-child(even), tr:nth-child(even), .custody-table tr:nth-child(even), .evaluation-table tr:nth-child(even), .contribution-table tr:nth-child(even), .rewards-table tr:nth-child(even), .deductions-table tr:nth-child(even), .vacation-table tr:nth-child(even), .report-table tr:nth-child(even), .delivery-table tr:nth-child(even), .undelivered-table tr:nth-child(even), .return-table tr:nth-child(even), .employee-table tr:nth-child(even), .vacations-table tr:nth-child(even), .added-vacations-table tr:nth-child(even), .vacations-details-table tr:nth-child(even), .custody-recipients-table tr:nth-child(even), .data-table tr:nth-child(even) {
  background-color: var(--bg-light);
}

/* ===== تنسيقات حاويات الجداول ===== */
.table-container, .custody-table-container, .evaluation-table-container, .contribution-table-container, .rewards-table-container, .deductions-table-container, .vacation-table-container {
  overflow-x: auto;
  width: 100%;
  margin-top: var(--spacing-lg);
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-heavy);
  padding: var(--spacing-lg);

}

.table-container:hover, .custody-table-container:hover, .evaluation-table-container:hover, .contribution-table-container:hover, .rewards-table-container:hover, .deductions-table-container:hover, .vacation-table-container:hover {
  box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

/* ===== تنسيقات البحث والتحكم بالجداول ===== */
.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.search-container {
  margin-bottom: var(--spacing-lg);
}

.search-input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  width: 100%;
  max-width: 300px;

  font-size: var(--font-size-base);
}

.search-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
}

/* ===== تنسيقات إضافية للجداول ===== */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-striped tbody tr:nth-child(odd) {
  background-color: var(--bg-white);
}

.table-striped tbody tr:nth-child(even) {
  background-color: var(--bg-light);
}

.table-bordered {
  border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--border-color);
}

.table-hover tbody tr:hover {
  background-color: var(--primary-light);
}

/* تنسيقات الجداول المتجاوبة */
@media (max-width: 768px) {
  .table-container, .custody-table-container, .evaluation-table-container, .contribution-table-container, .rewards-table-container, .deductions-table-container, .vacation-table-container {
    padding: var(--spacing-sm);
  }

  .table thead th, th, .custody-table th, .evaluation-table th, .contribution-table th, .rewards-table th, .deductions-table th, .vacation-table th, .report-table th, .delivery-table th, .undelivered-table th, .return-table th, .employee-table th, .vacations-table th, .added-vacations-table th, .vacations-details-table th, .custody-recipients-table th, .data-table th,
  .table tbody td, td, .custody-table td, .evaluation-table td, .contribution-table td, .rewards-table td, .deductions-table td, .vacation-table td, .report-table td, .delivery-table td, .undelivered-table td, .return-table td, .employee-table td, .vacations-table td, .added-vacations-table td, .vacations-details-table td, .custody-recipients-table td, .data-table td {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
  }

  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    max-width: 100%;
  }
}

/* ===== أنماط الموظفين المستقيلين ===== */
.resigned-employee {
  background-color: #ffebee !important;
  color: #c62828 !important;
  opacity: 0.7;
}

.resigned-employee td {
  background-color: #ffebee !important;
  color: #c62828 !important;
}

.resigned-employee .employee-name::before {
  content: "🚫 ";
  margin-right: 5px;
}

.resigned-status-badge {
  background-color: #f44336;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  margin-right: 5px;
}

.resigned-status-badge::before {
  content: "🔴 ";
}

/* أنماط للقوائم المنسدلة */
.resigned-option {
  background-color: #ffebee !important;
  color: #c62828 !important;
  font-style: italic;
}

.resigned-option::before {
  content: "🚫 ";
}

/* أنماط لبطاقة تفاصيل الموظف */
.employee-status-resigned {
  background-color: #f44336;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  display: inline-block;
  margin-bottom: 10px;
}

.employee-status-resigned::before {
  content: "⚠️ ";
}

.resignation-details {
  background-color: #fff3e0;
  border: 1px solid #ff9800;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.resignation-details h4 {
  color: #e65100;
  margin-top: 0;
}



/* ===== توحيد ألوان أزرار جدول الموظفين ===== */
/* تنسيقات خاصة لجدول الموظفين الرئيسي - توحيد الألوان */
.employee-table .view-btn,
.employee-table .edit-btn,
.employee-table .delete-btn,
.data-table .view-btn,
.data-table .edit-btn,
.data-table .delete-btn,
#employeeTable .view-btn,
#employeeTable .edit-btn,
#employeeTable .delete-btn,
table .view-btn,
table .edit-btn,
table .delete-btn {
  padding: 6px 10px !important;
  font-size: 12px !important;
  min-width: 60px !important;
  margin: 2px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  border: none !important;
}

/* توحيد ألوان الأزرار في جميع الجداول */
table .view-btn,
.employee-table .view-btn,
.data-table .view-btn,
#employeeTable .view-btn {
  background-color: #186F65 !important;
  color: white !important;
}

table .view-btn:hover,
.employee-table .view-btn:hover,
.data-table .view-btn:hover,
#employeeTable .view-btn:hover {
  background-color: #0F4C45 !important;
}

table .edit-btn,
.employee-table .edit-btn,
.data-table .edit-btn,
#employeeTable .edit-btn {
  background-color: #007bff !important;
  color: white !important;
}

table .edit-btn:hover,
.employee-table .edit-btn:hover,
.data-table .edit-btn:hover,
#employeeTable .edit-btn:hover {
  background-color: #0056b3 !important;
}

table .delete-btn,
.employee-table .delete-btn,
.data-table .delete-btn,
#employeeTable .delete-btn {
  background-color: #dc3545 !important;
  color: white !important;
}

table .delete-btn:hover,
.employee-table .delete-btn:hover,
.data-table .delete-btn:hover,
#employeeTable .delete-btn:hover {
  background-color: #c82333 !important;
}

/* ===== تنسيقات متجاوبة للنماذج ===== */
/* تنسيقات متجاوبة لنماذج التدريب والتقييم والفلاتر */
@media (max-width: 1200px) {
  .training-form,
  .evaluation-form {
    grid-template-columns: repeat(3, 1fr);
  }

  .training-form .form-actions,
  .evaluation-form .form-actions {
    grid-column: span 3;
  }

  .unified-filters .filter-row {
    grid-template-columns: repeat(3, 1fr);
  }

  .reports-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .report-card {
    min-height: 140px;
    padding: var(--spacing-lg);
  }

  .report-icon {
    font-size: 36px;
  }

  .report-value {
    font-size: 32px;
  }

  .report-footer {
    font-size: 14px;
    letter-spacing: 0.3px;
  }
}

@media (max-width: 900px) {
  .training-form,
  .evaluation-form {
    grid-template-columns: repeat(2, 1fr);
  }

  .training-form .form-actions,
  .evaluation-form .form-actions {
    grid-column: span 2;
  }

  .unified-filters .filter-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .reports-container {
    grid-template-columns: 1fr;
  }

  .report-card {
    min-height: 120px;
    padding: var(--spacing-md);
    border-radius: 12px;
  }

  .report-card::before {
    height: 3px;
    border-radius: 12px 12px 0 0;
  }

  .report-icon {
    font-size: 32px;
    margin-bottom: var(--spacing-sm);
  }

  .report-value {
    font-size: 28px;
    margin-bottom: var(--spacing-xs);
  }

  .report-footer {
    font-size: 13px;
    line-height: 1.3;
    letter-spacing: 0.2px;
  }
}

@media (max-width: 600px) {
  .training-form,
  .evaluation-form {
    grid-template-columns: 1fr;
  }

  .training-form .form-actions,
  .evaluation-form .form-actions {
    grid-column: span 1;
  }

  .unified-filters .filter-row {
    grid-template-columns: 1fr;
  }

  .reports-container {
    grid-template-columns: 1fr;
  }
}

/* ===== تنسيقات عامة للصف المميز (آخر إضافة) في جميع الجداول ===== */
/* هذه التنسيقات تطبق على الصف الأول في جميع الجداول لتمييز آخر إضافة */

.table tr:first-child,
.data-table tr:first-child,
table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50 !important;
}

.table tr:first-child td,
.data-table tr:first-child td,
table tr:first-child td {
  font-weight: bold !important;
}

/* تنسيقات خاصة لجداول محددة */
.extra-hours-table tbody tr:first-child,
.vacation-table tbody tr:first-child,
.contribution-table tbody tr:first-child,
.rewards-table tbody tr:first-child,
.deductions-table tbody tr:first-child,
.custody-table tbody tr:first-child,
.delivery-table tbody tr:first-child,
.evaluation-table tbody tr:first-child,
.ideal-employees-table tbody tr:first-child,
.training-table tbody tr:first-child,
.advances-table tbody tr:first-child,
.resignation-table tbody tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50 !important;
}

.extra-hours-table tbody tr:first-child td,
.vacation-table tbody tr:first-child td,
.contribution-table tbody tr:first-child td,
.rewards-table tbody tr:first-child td,
.deductions-table tbody tr:first-child td,
.custody-table tbody tr:first-child td,
.delivery-table tbody tr:first-child td,
.evaluation-table tbody tr:first-child td,
.ideal-employees-table tbody tr:first-child td,
.training-table tbody tr:first-child td,
.advances-table tbody tr:first-child td,
.resignation-table tbody tr:first-child td {
  font-weight: bold !important;
}

/* تأكيد أن التلوين يظهر حتى مع hover effects */
.table tr:first-child:hover,
.data-table tr:first-child:hover,
table tr:first-child:hover {
  background-color: #d4edda !important;
}