@import url('shared-styles.css');

.resignation-page {
  background: #f8f9fa;
  direction: rtl;
  text-align: right;
}

/* تحسين مظهر عمود الإدارة وعمود الملاحظات في الجداول */
.department-column {
  background: #eaf6ff;
  font-weight: bold;
  color: #1565c0;
  white-space: nowrap;
}
.notes-column {
  background: #fffbe7;
  color: #b26a00;
  font-style: italic;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-line;
}

/* تحسين مظهر عمود تاريخ ترك العمل */
.work-end-date-column {
  background: #f3e5f5;
  color: #7b1fa2;
  font-weight: 500;
  white-space: nowrap;
}

.resignation-page * {
  text-align: right;
}
.resignation-page .form-group label {
  text-align: right;
}
.resignation-page .tabs {
  direction: rtl;
  justify-content: flex-start;
}
.resignation-page .tab-btn {
  margin-right: 5px;
  margin-left: 0;
}
/* تم حذف جميع تنسيقات القائمة الجانبية - يتم التحكم بها من sidebar.css فقط */
.resignation-page h1,
.resignation-page h2,
.resignation-page h3 {
  text-align: right;
}

/* تنسيقات النماذج */
.resignation-form {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 15px;
}

.form-actions {
  grid-column: span 4;
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.resignation-table tr:last-child td {
  border-bottom: none;
}

/* تنسيق الصف المميز (آخر إضافة) */
.resignation-table tr:first-child {
  background-color: #e8f5e8 !important;
  border: 2px solid #4CAF50;
}

.resignation-table tr:first-child td {
  font-weight: bold;
}

/* تنسيق النافذة المنبثقة */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
  background-color: #fefefe;
  margin: 5% auto;
  padding: 20px;
  border: 1px solid #888;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

}

/* تم إزالة تأثير modalFadeIn لتحسين الأداء */

.close-modal {
  color: #aaa;
  float: left;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;

}

.close-modal:hover,
.close-modal:focus {
  color: #2196F3;
  text-decoration: none;
}

.modal .form-actions {
  grid-column: span 2;
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 25px;
}

/* Reports Tab Styles */
.unified-filters {
  background: #f7f7f7;
  border-radius: 8px;
  padding: 18px 20px 10px 20px;
  margin-bottom: 18px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.unified-filters .filter-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 18px;
}
.unified-filters .form-group {
  display: flex;
  flex-direction: column;
}
.unified-filters label {
  font-size: 14px;
  margin-bottom: 4px;
  color: #444;
}
.unified-filters input,
.unified-filters select {
  padding: 7px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 15px;
  color: #000;
}

/* إصلاح لون الخط في القوائم المنسدلة والخيارات */
select {
  color: #000 !important;
  background-color: #fff !important;
}

select option {
  color: #000 !important;
  background-color: #fff !important;
  font-weight: normal !important;
}

select option:hover {
  background-color: #f0f0f0 !important;
  color: #000 !important;
}

datalist {
  background-color: #fff !important;
}

datalist option {
  color: #000 !important;
  background-color: #fff !important;
  font-weight: normal !important;
  padding: 5px !important;
}

input[list] {
  color: #000 !important;
  background-color: #fff !important;
}

/* إصلاح لون الخط في اقتراحات البحث */
#resignationEmployeeSearchSuggestions,
#resignationEmployeeSearchSuggestions option {
  color: #000 !important;
  background-color: #fff !important;
  font-weight: normal !important;
}

#reportDepartment,
#reportDepartment option,
#reportReason,
#reportReason option,
#reportRecommendation,
#reportRecommendation option {
  color: #000 !important;
  background-color: #fff !important;
  font-weight: normal !important;
}

/* إصلاح إضافي للمتصفحات المختلفة */
.unified-filters select,
.unified-filters input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  color: #000 !important;
  background-color: #fff !important;
}

.unified-filters select:focus,
.unified-filters input:focus {
  outline: 2px solid #007bff;
  color: #000 !important;
}
.unified-filters .filter-actions {
  margin-top: 12px;
  display: flex;
  gap: 10px;
}

.reports-tables {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 18px 12px 12px 12px;
}

/* تنسيقات متجاوبة */
@media (max-width: 1200px) {
  .resignation-form {
    grid-template-columns: 1fr 1fr 1fr;
  }
  .form-actions {
    grid-column: span 3;
  }
}
@media (max-width: 900px) {
  .resignation-form {
    grid-template-columns: 1fr 1fr;
  }
  .form-actions {
    grid-column: span 2;
  }
  .unified-filters .filter-row {
    grid-template-columns: 1fr 1fr;
  }

}
@media (max-width: 600px) {
  .resignation-form {
    grid-template-columns: 1fr;
  }
  .form-actions {
    grid-column: span 1;
  }
  .unified-filters .filter-row {
    grid-template-columns: 1fr;
  }

}

/* رسائل عدم وجود صلاحيات */
.no-permission-message {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  text-align: center;
}

.no-permission-message p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

/* تنسيق خاص لخلايا الإجراءات عند عدم وجود صلاحيات */
.actions-column {
  min-width: 120px;
  text-align: center;
}

.actions-column:empty::after {
  content: "لا توجد إجراءات";
  color: #6c757d;
  font-style: italic;
}

/* تنسيق فلاتر البحث المحددة */
.filtered-search-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.search-filters-row .form-group {
  display: flex;
  flex-direction: column;
}

.search-filters-row .form-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
  font-size: 14px;
}

.filter-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
}

.filter-actions .reset-btn,
.filter-actions .export-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.filter-actions .reset-btn {
  background-color: #6c757d;
  color: white;
}

.filter-actions .reset-btn:hover {
  background-color: #5a6268;
}

.filter-actions .export-btn {
  background-color: #28a745;
  color: white;
}

.filter-actions .export-btn:hover {
  background-color: #218838;
}
