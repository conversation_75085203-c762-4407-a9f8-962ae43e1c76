const express = require('express');
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { logAction, createEditMessage } = require('../activityLogger');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

const router = express.Router();

// متغير لتتبع ما إذا تم إنشاء الجداول أم لا
let rewardsTableCreated = false;
let deductionsTableCreated = false;

// إنشاء جدول المكافآت إذا لم يكن موجودًا
const setupRewardsTable = async () => {
  if (rewardsTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً

  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS rewards (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) NOT NULL,
        employee_name varchar(255) DEFAULT NULL,
        department varchar(255) DEFAULT NULL,
        amount decimal(10,2) NOT NULL,
        reason text NOT NULL,
        reward_date date NOT NULL,
        notes text,
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_date (reward_date)
      ) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
    `);

    rewardsTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول المكافآت:', error);
    throw error;
  }
};

// إنشاء جدول الخصومات إذا لم يكن موجودًا
const setupDeductionsTable = async () => {
  if (deductionsTableCreated) return; // إذا تم إنشاء الجدول بالفعل، لا تفعل شيئاً

  try {
    await pool.promise().query(`
      CREATE TABLE IF NOT EXISTS deductions (
        id int NOT NULL AUTO_INCREMENT,
        employee_code varchar(50) NOT NULL,
        employee_name varchar(255) DEFAULT NULL,
        department varchar(255) DEFAULT NULL,
        amount int NOT NULL,
        reason text NOT NULL,
        date date NOT NULL,
        notes text,
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_employee_code (employee_code),
        KEY idx_date (date),
        KEY idx_department (department)
      ) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
    `);

    deductionsTableCreated = true; // تعيين المتغير إلى true بعد إنشاء الجدول
  } catch (error) {
    console.error('خطأ في إنشاء جدول الخصومات:', error);
    throw error;
  }
};

// إنشاء الجداول
router.get('/setup-tables', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    await setupRewardsTable();
    await setupDeductionsTable();
    res.json({ message: 'تم إنشاء جداول المكافآت والخصومات بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء الجداول:', error);
    res.status(500).json({ error: 'فشل في إنشاء الجداول' });
  }
});

// ==================== المكافآت ====================

// الحصول على جميع المكافآت
router.get('/', authenticateToken, checkPermission('view_rewards_list'), async (req, res) => {
  // إذا كان المسار يحتوي على rewards، نعرض المكافآت
  if (req.baseUrl.includes('rewards')) {
    try {
      const [rows] = await pool.promise().query(
        "SELECT * FROM rewards ORDER BY created_at DESC, id DESC"
      );

      res.json(rows);
    } catch (error) {
      console.error('خطأ في جلب المكافآت:', error);
      res.status(500).json({ error: 'فشل في جلب المكافآت' });
    }
    return;
  }
  
  // إذا كان المسار يحتوي على deductions، نعرض الخصومات
  if (req.baseUrl.includes('deductions')) {
    try {
      const [rows] = await pool.promise().query(
        "SELECT * FROM deductions ORDER BY created_at DESC, id DESC"
      );
      
      res.json(rows);
    } catch (error) {
      console.error('خطأ في جلب الخصومات:', error);
      res.status(500).json({ error: 'فشل في جلب الخصومات' });
    }
    return;
  }
});

// الحصول على جميع المكافآت (مسار بديل)
router.get('/rewards', authenticateToken, checkPermission('view_rewards_list'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(
      "SELECT * FROM rewards ORDER BY created_at DESC, id DESC"
    );

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب المكافآت:', error);
    res.status(500).json({ error: 'فشل في جلب المكافآت' });
  }
});

// البحث في المكافآت
router.get('/rewards/search', authenticateToken, checkPermission('view_rewards_list'), async (req, res) => {
  try {
    const { department, start_date, end_date, employee_code } = req.query;
    
    let query = "SELECT * FROM rewards WHERE 1=1";
    let params = [];
    
    if (department) {
      query += " AND department = ?";
      params.push(department);
    }
    
    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }
    
    if (start_date) {
      query += " AND reward_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND reward_date <= ?";
      params.push(end_date);
    }

    query += " ORDER BY created_at DESC, id DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث عن المكافآت:', error);
    res.status(500).json({ error: 'فشل في البحث عن المكافآت' });
  }
});

// إضافة مكافأة أو خصم جديد (مسار عام)
router.post('/', authenticateToken, async (req, res) => {
  // إذا كان المسار يحتوي على rewards، نضيف مكافأة
  if (req.baseUrl.includes('rewards')) {
    try {
      const { employee_code, employee_name, department, amount, reason, date, notes } = req.body;
      
      // التحقق من صحة البيانات
      if (!employee_code || !employee_name || !department || !amount || !reason || !date) {
        return res.status(400).json({ error: 'جميع الحقول مطلوبة باستثناء الملاحظات' });
      }
      
      if (amount <= 0) {
        return res.status(400).json({ error: 'مبلغ المكافأة يجب أن يكون أكبر من صفر' });
      }
      
      const [result] = await pool.promise().query(
        "INSERT INTO rewards (employee_code, employee_name, department, amount, reason, reward_date, notes) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [employee_code, employee_name, department, amount, reason, date, notes]
      );

      res.json({
        message: 'تم إضافة المكافأة بنجاح',
        id: result.insertId
      });
      
      res.json({ 
        message: 'تم إضافة المكافأة بنجاح', 
        id: result.insertId 
      });
    } catch (error) {
      console.error('خطأ في إضافة المكافأة:', error);
      res.status(500).json({ error: 'فشل في إضافة المكافأة' });
    }
    return;
  }
  
  // إذا كان المسار يحتوي على deductions، نضيف خصم
  if (req.baseUrl.includes('deductions')) {
    try {
      const { employee_code, employee_name, department, amount, reason, date, notes } = req.body;
      
      // التحقق من صحة البيانات
      if (!employee_code || !employee_name || !department || !amount || !reason || !date) {
        return res.status(400).json({ error: 'جميع الحقول مطلوبة باستثناء الملاحظات' });
      }
      
      if (amount <= 0) {
        return res.status(400).json({ error: 'مبلغ الخصم يجب أن يكون أكبر من صفر' });
      }
      
      const [result] = await pool.promise().query(
        "INSERT INTO deductions (employee_code, employee_name, department, amount, reason, date, notes) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [employee_code, employee_name, department, amount, reason, date, notes]
      );
      
      res.json({ 
        message: 'تم إضافة الخصم بنجاح', 
        id: result.insertId 
      });
    } catch (error) {
      console.error('خطأ في إضافة الخصم:', error);
      res.status(500).json({ error: 'فشل في إضافة الخصم' });
    }
    return;
  }
});

// إضافة مكافأة جديدة (مسار بديل)
router.post('/rewards', authenticateToken, checkPermission('add_reward'), async (req, res) => {
  try {
    const { employee_code, employee_name, department, amount, reason, date, notes } = req.body;
    
    // التحقق من صحة البيانات
    if (!employee_code || !employee_name || !department || !amount || !reason || !date) {
      return res.status(400).json({ error: 'جميع الحقول مطلوبة باستثناء الملاحظات' });
    }
    
    if (amount <= 0) {
      return res.status(400).json({ error: 'مبلغ المكافأة يجب أن يكون أكبر من صفر' });
    }
    
    const [result] = await pool.promise().query(
      "INSERT INTO rewards (employee_code, employee_name, department, amount, reason, reward_date, notes) VALUES (?, ?, ?, ?, ?, ?, ?)",
      [employee_code, employee_name, department, amount, reason, date, notes]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'rewards',
      record_id: result.insertId.toString(),
      message: `تم إضافة مكافأة للموظف: ${employee_name} (كود: ${employee_code}) بقيمة: ${amount} جنيه لسبب: ${reason}`
    });

    res.json({
      message: 'تم إضافة المكافأة بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة المكافأة:', error);
    res.status(500).json({ error: 'فشل في إضافة المكافأة' });
  }
});

// تعديل مكافأة
router.put('/rewards/:id', authenticateToken, checkPermission('add_reward'), async (req, res) => {
  try {
    const { id } = req.params;

    // تنظيف البيانات المرسلة من العميل
    const cleanData = cleanUpdateData(req.body);
    const { employee_code, employee_name, department, amount, reason, reward_date, notes } = cleanData;

    // التحقق من صحة البيانات
    if (!employee_code || !employee_name || !department || !amount || !reason || !reward_date) {
      return res.status(400).json({ error: 'جميع الحقول مطلوبة باستثناء الملاحظات' });
    }

    if (amount <= 0) {
      return res.status(400).json({ error: 'مبلغ المكافأة يجب أن يكون أكبر من صفر' });
    }

    // الحصول على البيانات القديمة قبل التعديل
    const [oldDataResult] = await pool.promise().query(
      "SELECT * FROM rewards WHERE id = ?",
      [id]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ error: 'المكافأة غير موجودة' });
    }

    const oldData = oldDataResult[0];

    // البيانات الجديدة
    const newData = {
      employee_code,
      employee_name,
      department,
      amount: parseInt(amount),
      reason,
      reward_date,
      notes: notes || null
    };

    // تحضير استعلام التحديث
    const { setClause, values } = prepareUpdateQuery(newData);

    const [result] = await pool.promise().query(
      `UPDATE rewards SET ${setClause} WHERE id = ?`,
      [...values, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'المكافأة غير موجودة' });
    }

    // إنشاء رسالة التعديل المفصلة
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'الإدارة',
      amount: 'مبلغ المكافأة',
      reason: 'سبب المكافأة',
      reward_date: 'تاريخ المكافأة',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `مكافأة للموظف: ${employee_name} (كود: ${employee_code})`,
      oldData,
      newData,
      fieldLabels
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'rewards',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تعديل المكافأة بنجاح' });
  } catch (error) {
    console.error('خطأ في تعديل المكافأة:', error);
    res.status(500).json({ error: 'فشل في تعديل المكافأة' });
  }
});

// حذف مكافأة
router.delete('/rewards/:id', authenticateToken, checkPermission('add_reward'), async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على معلومات المكافأة قبل الحذف
    const [rewardData] = await pool.promise().query(
      "SELECT employee_code, employee_name, amount, reason FROM rewards WHERE id = ?",
      [id]
    );

    if (rewardData.length === 0) {
      return res.status(404).json({ error: 'المكافأة غير موجودة' });
    }

    const reward = rewardData[0];

    // حذف المكافأة
    const [result] = await pool.promise().query(
      "DELETE FROM rewards WHERE id = ?",
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'rewards',
      record_id: id.toString(),
      message: `تم حذف مكافأة للموظف: ${reward.employee_name} (كود: ${reward.employee_code}) بقيمة: ${reward.amount} جنيه لسبب: ${reward.reason}`
    });

    res.json({ message: 'تم حذف المكافأة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المكافأة:', error);
    res.status(500).json({ error: 'فشل في حذف المكافأة' });
  }
});

// الحصول على مكافآت موظف محدد
router.get('/rewards/employee/:employee_code', authenticateToken, checkPermission('view_rewards_list'), async (req, res) => {
  try {
    const { employee_code } = req.params;

    const [rows] = await pool.promise().query(
      "SELECT * FROM rewards WHERE employee_code = ? ORDER BY created_at DESC, id DESC",
      [employee_code]
    );

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب مكافآت الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب مكافآت الموظف' });
  }
});

// ==================== الخصومات ====================

// الحصول على جميع الخصومات
router.get('/deductions', authenticateToken, checkPermission('view_deductions_list'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(
      "SELECT * FROM deductions ORDER BY created_at DESC, id DESC"
    );
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الخصومات:', error);
    res.status(500).json({ error: 'فشل في جلب الخصومات' });
  }
});

// البحث في الخصومات
router.get('/deductions/search', authenticateToken, checkPermission('view_deductions_list'), async (req, res) => {
  try {
    const { department, start_date, end_date, employee_code } = req.query;
    
    let query = "SELECT * FROM deductions WHERE 1=1";
    let params = [];
    
    if (department) {
      query += " AND department = ?";
      params.push(department);
    }
    
    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }
    
    if (start_date) {
      query += " AND date >= ?";
      params.push(start_date);
    }
    
    if (end_date) {
      query += " AND date <= ?";
      params.push(end_date);
    }
    
    query += " ORDER BY created_at DESC, id DESC";
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث عن الخصومات:', error);
    res.status(500).json({ error: 'فشل في البحث عن الخصومات' });
  }
});

// إضافة خصم جديد
router.post('/deductions', authenticateToken, checkPermission('add_deduction'), async (req, res) => {
  try {
    const { employee_code, employee_name, department, amount, reason, date, notes } = req.body;
    
    // التحقق من صحة البيانات
    if (!employee_code || !employee_name || !department || !amount || !reason || !date) {
      return res.status(400).json({ error: 'جميع الحقول مطلوبة باستثناء الملاحظات' });
    }
    
    if (amount <= 0) {
      return res.status(400).json({ error: 'مبلغ الخصم يجب أن يكون أكبر من صفر' });
    }
    
    const [result] = await pool.promise().query(
      "INSERT INTO deductions (employee_code, employee_name, department, amount, reason, date, notes) VALUES (?, ?, ?, ?, ?, ?, ?)",
      [employee_code, employee_name, department, amount, reason, date, notes]
    );
    
    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'penalties',
      record_id: result.insertId.toString(),
      message: `تم إضافة خصم للموظف: ${employee_name} (كود: ${employee_code}) بقيمة: ${amount} جنيه لسبب: ${reason}`
    });

    res.json({
      message: 'تم إضافة الخصم بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة الخصم:', error);
    res.status(500).json({ error: 'فشل في إضافة الخصم' });
  }
});

// تعديل خصم
router.put('/deductions/:id', authenticateToken, checkPermission('add_deduction'), async (req, res) => {
  try {
    const { id } = req.params;

    // تنظيف البيانات المرسلة من العميل
    const cleanData = cleanUpdateData(req.body);
    const { employee_code, employee_name, department, amount, reason, date, notes } = cleanData;

    // التحقق من صحة البيانات
    if (!employee_code || !employee_name || !department || !amount || !reason || !date) {
      return res.status(400).json({ error: 'جميع الحقول مطلوبة باستثناء الملاحظات' });
    }

    if (amount <= 0) {
      return res.status(400).json({ error: 'مبلغ الخصم يجب أن يكون أكبر من صفر' });
    }

    // الحصول على البيانات القديمة قبل التعديل
    const [oldDataResult] = await pool.promise().query(
      "SELECT * FROM deductions WHERE id = ?",
      [id]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ error: 'الخصم غير موجود' });
    }

    const oldData = oldDataResult[0];

    // البيانات الجديدة
    const newData = {
      employee_code,
      employee_name,
      department,
      amount: parseInt(amount),
      reason,
      date,
      notes: notes || null
    };

    // تحضير استعلام التحديث
    const { setClause, values } = prepareUpdateQuery(newData);

    const [result] = await pool.promise().query(
      `UPDATE deductions SET ${setClause} WHERE id = ?`,
      [...values, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'الخصم غير موجود' });
    }

    // إنشاء رسالة التعديل المفصلة
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'الإدارة',
      amount: 'مبلغ الخصم',
      reason: 'سبب الخصم',
      date: 'تاريخ الخصم',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `خصم للموظف: ${employee_name} (كود: ${employee_code})`,
      oldData,
      newData,
      fieldLabels
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'penalties',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تعديل الخصم بنجاح' });
  } catch (error) {
    console.error('خطأ في تعديل الخصم:', error);
    res.status(500).json({ error: 'فشل في تعديل الخصم' });
  }
});

// حذف خصم
router.delete('/deductions/:id', authenticateToken, checkPermission('add_deduction'), async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على معلومات الخصم قبل الحذف
    const [deductionData] = await pool.promise().query(
      "SELECT employee_code, employee_name, amount, reason FROM deductions WHERE id = ?",
      [id]
    );

    if (deductionData.length === 0) {
      return res.status(404).json({ error: 'الخصم غير موجود' });
    }

    const deduction = deductionData[0];

    // حذف الخصم
    const [result] = await pool.promise().query(
      "DELETE FROM deductions WHERE id = ?",
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'penalties',
      record_id: id.toString(),
      message: `تم حذف خصم للموظف: ${deduction.employee_name} (كود: ${deduction.employee_code}) بقيمة: ${deduction.amount} جنيه لسبب: ${deduction.reason}`
    });

    res.json({ message: 'تم حذف الخصم بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الخصم:', error);
    res.status(500).json({ error: 'فشل في حذف الخصم' });
  }
});

// الحصول على خصومات موظف محدد
router.get('/deductions/employee/:employee_code', authenticateToken, checkPermission('view_deductions_list'), async (req, res) => {
  try {
    const { employee_code } = req.params;
    
    const [rows] = await pool.promise().query(
      "SELECT * FROM deductions WHERE employee_code = ? ORDER BY created_at DESC, id DESC",
      [employee_code]
    );
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب خصومات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب خصومات الموظف' });
  }
});

// ==================== إحصائيات ====================

// الحصول على إحصائيات المكافآت والخصومات
router.get('/statistics', authenticateToken, checkPermission('view_rewards_deductions'), async (req, res) => {
  try {
    const { start_date, end_date, department } = req.query;
    
    let whereClause = "WHERE 1=1";
    let params = [];
    
    if (start_date) {
      whereClause += " AND reward_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      whereClause += " AND reward_date <= ?";
      params.push(end_date);
    }
    
    if (department) {
      whereClause += " AND department = ?";
      params.push(department);
    }
    
    // إحصائيات المكافآت
    const [rewardsStats] = await pool.promise().query(
      `SELECT 
        COUNT(*) as total_count,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        MAX(amount) as max_amount,
        MIN(amount) as min_amount
      FROM rewards ${whereClause}`,
      params
    );
    
    // إحصائيات الخصومات
    const [deductionsStats] = await pool.promise().query(
      `SELECT 
        COUNT(*) as total_count,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        MAX(amount) as max_amount,
        MIN(amount) as min_amount
      FROM deductions ${whereClause}`,
      params
    );
    
    res.json({
      rewards: rewardsStats[0],
      deductions: deductionsStats[0]
    });
  } catch (error) {
    console.error('خطأ في جلب الإحصائيات:', error);
    res.status(500).json({ error: 'فشل في جلب الإحصائيات' });
  }
});

// الحصول على إحصائيات موظف محدد
router.get('/statistics/employee/:employee_code', authenticateToken, checkPermission('view_rewards_deductions'), async (req, res) => {
  try {
    const { employee_code } = req.params;
    const { start_date, end_date } = req.query;
    
    let whereClause = "WHERE employee_code = ?";
    let params = [employee_code];
    
    if (start_date) {
      whereClause += " AND reward_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      whereClause += " AND reward_date <= ?";
      params.push(end_date);
    }
    
    // إحصائيات المكافآت للموظف
    const [rewardsStats] = await pool.promise().query(
      `SELECT 
        COUNT(*) as total_count,
        SUM(amount) as total_amount
      FROM rewards ${whereClause}`,
      params
    );
    
    // إحصائيات الخصومات للموظف
    const [deductionsStats] = await pool.promise().query(
      `SELECT 
        COUNT(*) as total_count,
        SUM(amount) as total_amount
      FROM deductions ${whereClause}`,
      params
    );
    
    res.json({
      employee_code,
      rewards: rewardsStats[0],
      deductions: deductionsStats[0],
      net_amount: (rewardsStats[0].total_amount || 0) - (deductionsStats[0].total_amount || 0)
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات الموظف' });
  }
});

module.exports = router;