// متغيرات عامة
const API_URL = 'http://localhost:5500/api';
let employees = [];
let trainingCourses = [];
let filteredCourses = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  initializeTrainingPage();
});

// تهيئة صفحة التدريب
async function initializeTrainingPage() {
  try {
    await loadEmployees();
    await loadTrainingCourses();
    setupEventListeners();
    loadDepartments();

    // التحقق من المحتوى المحدد
    setTimeout(() => {
      checkSelectedContent();
    }, 100);
  } catch (error) {
    console.error('خطأ في تهيئة صفحة التدريب:', error);
    alert('حدث خطأ في تحميل البيانات');
  }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
  // البحث عن الموظف
  const employeeSearchInput = document.getElementById('employeeSearchAdd');
  if (employeeSearchInput) {
    employeeSearchInput.addEventListener('input', handleEmployeeSearch);
    employeeSearchInput.addEventListener('change', handleEmployeeSelection);

    // إضافة مستمع للضغط على Enter
    employeeSearchInput.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleEmployeeSelection.call(this);
      }
    });

    // إضافة مستمع لفقدان التركيز مع تأخير قصير
    employeeSearchInput.addEventListener('blur', function() {
      setTimeout(() => {
        handleEmployeeSelection.call(this);
      }, 100);
    });
  }

  // حفظ الدورة التدريبية
  const saveTrainingBtn = document.getElementById('saveTraining');
  if (saveTrainingBtn) {
    saveTrainingBtn.addEventListener('click', saveTrainingCourse);
  }

  // إعادة تعيين النموذج
  const resetFormBtn = document.getElementById('resetTrainingForm');
  if (resetFormBtn) {
    resetFormBtn.addEventListener('click', resetTrainingForm);
  }

  // البحث في الجدول
  const searchInput = document.getElementById('searchTraining');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      searchInTrainingTable(this.value);
    });
  }

  // تصدير البيانات
  const exportBtn = document.getElementById('exportTrainingBtn');
  if (exportBtn) {
    exportBtn.addEventListener('click', exportTrainingData);
  }

  // إنشاء التقرير
  const generateReportBtn = document.getElementById('generateReport');
  if (generateReportBtn) {
    generateReportBtn.addEventListener('click', generateTrainingReport);
  }

  // تصدير التقرير
  const exportReportBtn = document.getElementById('exportReportBtn');
  if (exportReportBtn) {
    exportReportBtn.addEventListener('click', exportTrainingReport);
  }

  // إعداد النافذة المنبثقة للتعديل
  setupEditModal();
}

// إعداد النافذة المنبثقة للتعديل
function setupEditModal() {
  // البحث عن الموظف في النافذة المنبثقة
  const editEmployeeSearchInput = document.getElementById('editEmployeeSearch');
  if (editEmployeeSearchInput) {
    editEmployeeSearchInput.addEventListener('input', handleEditEmployeeSearch);
    editEmployeeSearchInput.addEventListener('change', handleEditEmployeeSelection);

    editEmployeeSearchInput.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleEditEmployeeSelection.call(this);
      }
    });

    editEmployeeSearchInput.addEventListener('blur', function() {
      setTimeout(() => {
        handleEditEmployeeSelection.call(this);
      }, 100);
    });
  }

  // تحديث الدورة التدريبية
  const updateTrainingBtn = document.getElementById('updateTraining');
  if (updateTrainingBtn) {
    updateTrainingBtn.addEventListener('click', updateTrainingCourse);
  }

  // إغلاق النافذة المنبثقة بمفتاح Escape
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      const modal = document.getElementById('editTrainingModal');
      if (modal && modal.style.display === 'flex') {
        closeEditModal();
      }
    }
  });
}

// التحقق من المحتوى المحدد من البطاقات
function checkSelectedContent() {
  const selectedContent = localStorage.getItem('selectedTrainingTab');

  if (selectedContent) {
    // حذف المحتوى المحفوظ
    localStorage.removeItem('selectedTrainingTab');

    // عرض المحتوى المناسب
    showContent(selectedContent);
  } else {
    // عرض المحتوى الافتراضي (إضافة دورة تدريبية)
    showContent('add-training');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-training') {
        pageTitle.textContent = 'إضافة دورة تدريبية';
      } else if (contentType === 'training-reports') {
        pageTitle.textContent = 'تقارير التدريب';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'training-reports') {
      loadDepartments();
    }
  } else {
    console.error('لم يتم العثور على المحتوى:', contentType);
  }
}

// تحميل قائمة الموظفين
async function loadEmployees() {
  try {
    console.log('بدء تحميل الموظفين...');
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/employees`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      employees = await response.json();
      console.log('تم تحميل الموظفين بنجاح:', employees.length);
      console.log('أول موظف:', employees[0]);
    } else {
      console.error('فشل في تحميل الموظفين - كود الاستجابة:', response.status);
      const errorText = await response.text();
      console.error('رسالة الخطأ:', errorText);
    }
  } catch (error) {
    console.error('خطأ في تحميل الموظفين:', error);
    alert('حدث خطأ في تحميل قائمة الموظفين. يرجى التحقق من الاتصال بالخادم.');
  }
}

// تحميل الدورات التدريبية
async function loadTrainingCourses() {
  try {
    console.log('بدء تحميل الدورات التدريبية...');
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      trainingCourses = await response.json();
      filteredCourses = [...trainingCourses];
      displayTrainingCourses();
      console.log('تم تحميل الدورات التدريبية بنجاح:', trainingCourses.length);
    } else {
      console.error('فشل في تحميل الدورات التدريبية - كود الاستجابة:', response.status);
      // إنشاء جدول التدريب إذا لم يكن موجوداً
      await createTrainingTable();
    }
  } catch (error) {
    console.error('خطأ في تحميل الدورات التدريبية:', error);
    // إنشاء جدول التدريب إذا لم يكن موجوداً
    await createTrainingTable();
  }
}

// إنشاء جدول التدريب
async function createTrainingTable() {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training/create-table`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      console.log('تم إنشاء جدول التدريب');
      await loadTrainingCourses();
    }
  } catch (error) {
    console.error('خطأ في إنشاء جدول التدريب:', error);
  }
}

// البحث عن الموظف
function handleEmployeeSearch() {
  const searchValue = this.value.toLowerCase().trim();
  const datalist = document.getElementById('employeeSearchSuggestions');

  if (!datalist) return;

  datalist.innerHTML = '';

  if (searchValue.length < 1) {
    // إذا كان البحث فارغ، نفرغ الحقول
    document.getElementById('employeeCode').value = '';
    document.getElementById('employeeName').value = '';
    document.getElementById('employeeDepartment').value = '';
    return;
  }

  // فلترة الموظفين النشطين فقط
  const filteredEmployees = employees.filter(emp => {
    if (emp.status === 'مستقيل') return false;

    const codeMatch = emp.code.toString().includes(searchValue);
    const nameMatch = emp.full_name.toLowerCase().includes(searchValue);
    const deptMatch = emp.department && emp.department.toLowerCase().includes(searchValue);

    return codeMatch || nameMatch || deptMatch;
  });

  // ترتيب النتائج: التطابق الدقيق للكود أولاً، ثم بداية الاسم، ثم باقي النتائج
  filteredEmployees.sort((a, b) => {
    const aCodeExact = a.code.toString() === searchValue;
    const bCodeExact = b.code.toString() === searchValue;

    if (aCodeExact && !bCodeExact) return -1;
    if (!aCodeExact && bCodeExact) return 1;

    const aCodeStart = a.code.toString().startsWith(searchValue);
    const bCodeStart = b.code.toString().startsWith(searchValue);

    if (aCodeStart && !bCodeStart) return -1;
    if (!aCodeStart && bCodeStart) return 1;

    const aNameStart = a.full_name.toLowerCase().startsWith(searchValue);
    const bNameStart = b.full_name.toLowerCase().startsWith(searchValue);

    if (aNameStart && !bNameStart) return -1;
    if (!aNameStart && bNameStart) return 1;

    return a.full_name.localeCompare(b.full_name, 'ar');
  });

  // عرض أفضل 10 نتائج
  filteredEmployees.slice(0, 10).forEach(emp => {
    const option = document.createElement('option');
    option.value = `${emp.code} - ${emp.full_name}`;
    option.dataset.code = emp.code;
    option.dataset.name = emp.full_name;
    option.dataset.department = emp.department || '';
    datalist.appendChild(option);
  });

  console.log('Search results:', filteredEmployees.length, 'employees found for:', searchValue);
}

// اختيار الموظف
function handleEmployeeSelection() {
  const selectedValue = this.value.trim();
  console.log('Selected value:', selectedValue);

  // إذا كان الحقل فارغ، نفرغ جميع الحقول
  if (selectedValue === '') {
    document.getElementById('employeeCode').value = '';
    document.getElementById('employeeName').value = '';
    document.getElementById('employeeDepartment').value = '';
    return;
  }

  // البحث في datalist أولاً بطريقة دقيقة
  const datalistOptions = document.querySelectorAll('#employeeSearchSuggestions option');
  let foundOption = null;

  for (let option of datalistOptions) {
    if (option.value === selectedValue) {
      foundOption = option;
      break;
    }
  }

  if (foundOption) {
    console.log('Found exact option:', foundOption);
    document.getElementById('employeeCode').value = foundOption.dataset.code;
    document.getElementById('employeeName').value = foundOption.dataset.name;
    document.getElementById('employeeDepartment').value = foundOption.dataset.department || '';
    return;
  }

  // إذا لم نجد تطابق دقيق، نحاول استخراج الكود من بداية النص
  const codeMatch = selectedValue.match(/^(\d+)/);
  if (codeMatch) {
    const extractedCode = codeMatch[1];
    const foundEmployee = employees.find(emp => emp.code.toString() === extractedCode);

    if (foundEmployee) {
      console.log('Found employee by extracted code:', foundEmployee);
      document.getElementById('employeeCode').value = foundEmployee.code;
      document.getElementById('employeeName').value = foundEmployee.full_name;
      document.getElementById('employeeDepartment').value = foundEmployee.department || '';

      // تحديث قيمة البحث لتظهر بالتنسيق الصحيح
      this.value = `${foundEmployee.code} - ${foundEmployee.full_name}`;
      return;
    }
  }

  // البحث المباشر بالكود فقط
  const directCodeEmployee = employees.find(emp => emp.code.toString() === selectedValue);
  if (directCodeEmployee) {
    console.log('Found employee by direct code:', directCodeEmployee);
    document.getElementById('employeeCode').value = directCodeEmployee.code;
    document.getElementById('employeeName').value = directCodeEmployee.full_name;
    document.getElementById('employeeDepartment').value = directCodeEmployee.department || '';

    this.value = `${directCodeEmployee.code} - ${directCodeEmployee.full_name}`;
    return;
  }

  console.log('No exact match found for:', selectedValue);
}

// البحث عن الموظف في النافذة المنبثقة
function handleEditEmployeeSearch() {
  const searchValue = this.value.toLowerCase().trim();
  const datalist = document.getElementById('editEmployeeSearchSuggestions');

  if (!datalist) return;

  datalist.innerHTML = '';

  if (searchValue.length < 1) {
    document.getElementById('editEmployeeCode').value = '';
    document.getElementById('editEmployeeName').value = '';
    document.getElementById('editEmployeeDepartment').value = '';
    return;
  }

  const filteredEmployees = employees.filter(emp => {
    if (emp.status === 'مستقيل') return false;

    const codeMatch = emp.code.toString().includes(searchValue);
    const nameMatch = emp.full_name.toLowerCase().includes(searchValue);
    const deptMatch = emp.department && emp.department.toLowerCase().includes(searchValue);

    return codeMatch || nameMatch || deptMatch;
  });

  filteredEmployees.sort((a, b) => {
    const aCodeExact = a.code.toString() === searchValue;
    const bCodeExact = b.code.toString() === searchValue;

    if (aCodeExact && !bCodeExact) return -1;
    if (!aCodeExact && bCodeExact) return 1;

    const aCodeStart = a.code.toString().startsWith(searchValue);
    const bCodeStart = b.code.toString().startsWith(searchValue);

    if (aCodeStart && !bCodeStart) return -1;
    if (!aCodeStart && bCodeStart) return 1;

    const aNameStart = a.full_name.toLowerCase().startsWith(searchValue);
    const bNameStart = b.full_name.toLowerCase().startsWith(searchValue);

    if (aNameStart && !bNameStart) return -1;
    if (!aNameStart && bNameStart) return 1;

    return a.full_name.localeCompare(b.full_name, 'ar');
  });

  filteredEmployees.slice(0, 10).forEach(emp => {
    const option = document.createElement('option');
    option.value = `${emp.code} - ${emp.full_name}`;
    option.dataset.code = emp.code;
    option.dataset.name = emp.full_name;
    option.dataset.department = emp.department || '';
    datalist.appendChild(option);
  });

  console.log('Edit modal search results:', filteredEmployees.length, 'employees found for:', searchValue);
}

// اختيار الموظف في النافذة المنبثقة
function handleEditEmployeeSelection() {
  const selectedValue = this.value.trim();
  console.log('Edit modal selected value:', selectedValue);

  if (selectedValue === '') {
    document.getElementById('editEmployeeCode').value = '';
    document.getElementById('editEmployeeName').value = '';
    document.getElementById('editEmployeeDepartment').value = '';
    return;
  }

  const datalistOptions = document.querySelectorAll('#editEmployeeSearchSuggestions option');
  let foundOption = null;

  for (let option of datalistOptions) {
    if (option.value === selectedValue) {
      foundOption = option;
      break;
    }
  }

  if (foundOption) {
    console.log('Edit modal found exact option:', foundOption);
    document.getElementById('editEmployeeCode').value = foundOption.dataset.code;
    document.getElementById('editEmployeeName').value = foundOption.dataset.name;
    document.getElementById('editEmployeeDepartment').value = foundOption.dataset.department || '';
    return;
  }

  const codeMatch = selectedValue.match(/^(\d+)/);
  if (codeMatch) {
    const extractedCode = codeMatch[1];
    const foundEmployee = employees.find(emp => emp.code.toString() === extractedCode);

    if (foundEmployee) {
      console.log('Edit modal found employee by extracted code:', foundEmployee);
      document.getElementById('editEmployeeCode').value = foundEmployee.code;
      document.getElementById('editEmployeeName').value = foundEmployee.full_name;
      document.getElementById('editEmployeeDepartment').value = foundEmployee.department || '';
      this.value = `${foundEmployee.code} - ${foundEmployee.full_name}`;
      return;
    }
  }

  const directCodeEmployee = employees.find(emp => emp.code.toString() === selectedValue);
  if (directCodeEmployee) {
    console.log('Edit modal found employee by direct code:', directCodeEmployee);
    document.getElementById('editEmployeeCode').value = directCodeEmployee.code;
    document.getElementById('editEmployeeName').value = directCodeEmployee.full_name;
    document.getElementById('editEmployeeDepartment').value = directCodeEmployee.department || '';
    this.value = `${directCodeEmployee.code} - ${directCodeEmployee.full_name}`;
    return;
  }

  console.log('Edit modal: No exact match found for:', selectedValue);
}

// وظيفة للتحقق من صحة بيانات الموظف المختار
function validateSelectedEmployee() {
  const employeeCode = document.getElementById('employeeCode').value;
  const employeeName = document.getElementById('employeeName').value;
  const searchValue = document.getElementById('employeeSearchAdd').value;

  if (employeeCode && employeeName) {
    // التحقق من أن البيانات المعروضة تطابق الموظف الصحيح
    const employee = employees.find(emp => emp.code.toString() === employeeCode);
    if (employee && employee.full_name === employeeName) {
      console.log('Employee data validated successfully');
      return true;
    } else {
      console.warn('Employee data mismatch detected');
      // إعادة تعيين البيانات
      document.getElementById('employeeCode').value = '';
      document.getElementById('employeeName').value = '';
      document.getElementById('employeeDepartment').value = '';
      return false;
    }
  }
  return false;
}

// حفظ الدورة التدريبية
async function saveTrainingCourse() {
  const employeeCode = document.getElementById('employeeCode').value;
  const employeeName = document.getElementById('employeeName').value;
  const department = document.getElementById('employeeDepartment').value;
  const courseName = document.getElementById('courseName').value;
  const courseDate = document.getElementById('courseDate').value;
  const courseDuration = document.getElementById('courseDuration').value;
  const trainingType = document.getElementById('trainingType').value;
  const courseCost = document.getElementById('courseCost').value || 0;
  const notes = document.getElementById('trainingNotes').value;

  // التحقق من صحة البيانات الأساسية
  if (!employeeCode || !employeeName || !courseName || !courseDate || !courseDuration || !trainingType) {
    alert('يرجى ملء جميع الحقول المطلوبة');
    return;
  }

  // التحقق من صحة بيانات الموظف المختار
  const selectedEmployee = employees.find(emp => emp.code.toString() === employeeCode);
  if (!selectedEmployee) {
    alert('الموظف المختار غير صحيح. يرجى البحث واختيار موظف صحيح.');
    return;
  }

  if (selectedEmployee.full_name !== employeeName) {
    alert('بيانات الموظف غير متطابقة. يرجى البحث واختيار الموظف مرة أخرى.');
    // إعادة تعيين بيانات الموظف
    document.getElementById('employeeCode').value = selectedEmployee.code;
    document.getElementById('employeeName').value = selectedEmployee.full_name;
    document.getElementById('employeeDepartment').value = selectedEmployee.department || '';
    document.getElementById('employeeSearchAdd').value = `${selectedEmployee.code} - ${selectedEmployee.full_name}`;
    return;
  }

  const trainingData = {
    employee_code: employeeCode,
    employee_name: employeeName,
    department: department,
    course_name: courseName,
    course_date: courseDate,
    course_duration: parseInt(courseDuration),
    training_type: trainingType,
    course_cost: parseFloat(courseCost),
    notes: notes
  };

  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(trainingData)
    });

    if (response.ok) {
      alert('تم حفظ الدورة التدريبية بنجاح');
      await loadTrainingCourses();
      resetTrainingForm();
    } else {
      const errorData = await response.json();
      alert(errorData.error || 'فشل في حفظ الدورة التدريبية');
    }
  } catch (error) {
    console.error('خطأ في حفظ الدورة التدريبية:', error);
    alert('حدث خطأ أثناء حفظ الدورة التدريبية');
  }
}

// إعادة تعيين النموذج
function resetTrainingForm() {
  document.getElementById('employeeSearchAdd').value = '';
  document.getElementById('employeeCode').value = '';
  document.getElementById('employeeName').value = '';
  document.getElementById('employeeDepartment').value = '';
  document.getElementById('courseName').value = '';
  document.getElementById('courseDate').value = '';
  document.getElementById('courseDuration').value = '';
  document.getElementById('trainingType').value = '';
  document.getElementById('courseCost').value = '';
  document.getElementById('trainingNotes').value = '';

  // تفريغ datalist
  const datalist = document.getElementById('employeeSearchSuggestions');
  if (datalist) {
    datalist.innerHTML = '';
  }

  const saveBtn = document.getElementById('saveTraining');
  saveBtn.textContent = 'حفظ الدورة التدريبية';
}

// عرض الدورات التدريبية
function displayTrainingCourses() {
  const tableBody = document.querySelector('#training-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
  const sortedCourses = [...filteredCourses].sort((a, b) => {
    // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
    const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
    const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
    return bTime - aTime; // الأحدث أولاً
  });

  sortedCourses.forEach((course, index) => {
    const row = document.createElement('tr');

    // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
    if (index === 0) {
      row.style.backgroundColor = '#e8f5e8';
      row.style.border = '2px solid #4CAF50';
    }

    row.innerHTML = `
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.employee_code}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.employee_name}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.department || ''}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.course_name}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatDate(course.course_date)}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.course_duration} يوم</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.training_type}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.course_cost} جنيه</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${course.notes || ''}</td>
      <td>
        ${hasPermission('edit_training') ? `<button class="edit-btn" onclick="editTrainingCourse(${course.id})" title="تعديل">
          <i class="fas fa-edit"></i>
        </button>` : ''}
        ${hasPermission('delete_training') ? `<button class="delete-btn" onclick="deleteTrainingCourse(${course.id})" title="حذف">
          <i class="fas fa-trash-alt"></i>
        </button>` : ''}
      </td>
    `;
    tableBody.appendChild(row);
  });
}

// فحص الصلاحيات
function hasPermission(permission) {
  try {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
    const result = permissions[permission] === true;
    console.log(`[Training] hasPermission(${permission}) = ${result}`, permissions);
    return result;
  } catch (error) {
    console.error('خطأ في قراءة الصلاحيات:', error);
    return false;
  }
}

// تعديل الدورة التدريبية - فتح النافذة المنبثقة
function editTrainingCourse(id) {
  // فحص صلاحية التعديل
  if (!hasPermission('edit_training')) {
    alert('ليس لديك صلاحية لتعديل الدورات التدريبية');
    return;
  }

  const course = trainingCourses.find(c => c.id === id);
  if (!course) return;

  // ملء بيانات النافذة المنبثقة
  document.getElementById('editEmployeeSearch').value = `${course.employee_code} - ${course.employee_name}`;
  document.getElementById('editEmployeeCode').value = course.employee_code;
  document.getElementById('editEmployeeName').value = course.employee_name;
  document.getElementById('editEmployeeDepartment').value = course.department || '';
  document.getElementById('editCourseName').value = course.course_name;
  // استخدام DateUtils لتنسيق التاريخ بشكل صحيح
  if (typeof DateUtils !== 'undefined') {
    document.getElementById('editCourseDate').value = DateUtils.formatDateForInput(course.course_date);
  } else {
    document.getElementById('editCourseDate').value = formatDateForInput(course.course_date);
  }
  document.getElementById('editCourseDuration').value = course.course_duration;
  document.getElementById('editTrainingType').value = course.training_type;
  document.getElementById('editCourseCost').value = course.course_cost;
  document.getElementById('editTrainingNotes').value = course.notes || '';

  // حفظ معرف الدورة للتحديث
  const updateBtn = document.getElementById('updateTraining');
  updateBtn.dataset.editId = id;

  // إظهار النافذة المنبثقة
  document.getElementById('editTrainingModal').style.display = 'flex';
}

// إغلاق النافذة المنبثقة
function closeEditModal() {
  document.getElementById('editTrainingModal').style.display = 'none';

  // تفريغ النموذج
  document.getElementById('editEmployeeSearch').value = '';
  document.getElementById('editEmployeeCode').value = '';
  document.getElementById('editEmployeeName').value = '';
  document.getElementById('editEmployeeDepartment').value = '';
  document.getElementById('editCourseName').value = '';
  document.getElementById('editCourseDate').value = '';
  document.getElementById('editCourseDuration').value = '';
  document.getElementById('editTrainingType').value = '';
  document.getElementById('editCourseCost').value = '';
  document.getElementById('editTrainingNotes').value = '';

  // تفريغ datalist
  const datalist = document.getElementById('editEmployeeSearchSuggestions');
  if (datalist) {
    datalist.innerHTML = '';
  }

  // إزالة معرف التحديث
  const updateBtn = document.getElementById('updateTraining');
  delete updateBtn.dataset.editId;
}

// تحديث الدورة التدريبية
async function updateTrainingCourse() {
  const updateBtn = document.getElementById('updateTraining');
  const editId = updateBtn.dataset.editId;

  if (!editId) {
    alert('خطأ: لم يتم تحديد الدورة للتحديث');
    return;
  }

  const employeeCode = document.getElementById('editEmployeeCode').value;
  const employeeName = document.getElementById('editEmployeeName').value;
  const department = document.getElementById('editEmployeeDepartment').value;
  const courseName = document.getElementById('editCourseName').value;
  const courseDate = document.getElementById('editCourseDate').value;
  const courseDuration = document.getElementById('editCourseDuration').value;
  const trainingType = document.getElementById('editTrainingType').value;
  const courseCost = document.getElementById('editCourseCost').value || 0;
  const notes = document.getElementById('editTrainingNotes').value;

  // التحقق من صحة البيانات الأساسية
  if (!employeeCode || !employeeName || !courseName || !courseDate || !courseDuration || !trainingType) {
    alert('يرجى ملء جميع الحقول المطلوبة');
    return;
  }

  // التحقق من صحة بيانات الموظف المختار
  const selectedEmployee = employees.find(emp => emp.code.toString() === employeeCode);
  if (!selectedEmployee) {
    alert('الموظف المختار غير صحيح. يرجى البحث واختيار موظف صحيح.');
    return;
  }

  if (selectedEmployee.full_name !== employeeName) {
    alert('بيانات الموظف غير متطابقة. يرجى البحث واختيار الموظف مرة أخرى.');
    return;
  }

  const trainingData = {
    employee_code: employeeCode,
    employee_name: employeeName,
    department: department,
    course_name: courseName,
    course_date: courseDate,
    course_duration: parseInt(courseDuration),
    training_type: trainingType,
    course_cost: parseFloat(courseCost),
    notes: notes
  };

  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training/${editId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(trainingData)
    });

    if (response.ok) {
      alert('تم تحديث الدورة التدريبية بنجاح');
      await loadTrainingCourses();
      closeEditModal();
    } else {
      const errorData = await response.json();
      alert(errorData.error || 'فشل في تحديث الدورة التدريبية');
    }
  } catch (error) {
    console.error('خطأ في تحديث الدورة التدريبية:', error);
    alert('حدث خطأ أثناء تحديث الدورة التدريبية');
  }
}

// حذف الدورة التدريبية
async function deleteTrainingCourse(id) {
  // فحص صلاحية الحذف
  if (!hasPermission('delete_training')) {
    alert('ليس لديك صلاحية لحذف الدورات التدريبية');
    return;
  }

  if (!confirm('هل أنت متأكد من حذف هذه الدورة التدريبية؟')) return;

  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/training/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      alert('تم حذف الدورة التدريبية بنجاح');
      await loadTrainingCourses();
    } else {
      alert('فشل في حذف الدورة التدريبية');
    }
  } catch (error) {
    console.error('خطأ في حذف الدورة التدريبية:', error);
    alert('حدث خطأ أثناء حذف الدورة التدريبية');
  }
}

// البحث في جدول الدورات التدريبية
function searchInTrainingTable(searchTerm) {
  if (!searchTerm) {
    filteredCourses = [...trainingCourses];
  } else {
    const term = searchTerm.toLowerCase();
    filteredCourses = trainingCourses.filter(course =>
      course.employee_code.toString().includes(term) ||
      course.employee_name.toLowerCase().includes(term) ||
      course.course_name.toLowerCase().includes(term) ||
      course.training_type.toLowerCase().includes(term) ||
      (course.department && course.department.toLowerCase().includes(term))
    );
  }
  displayTrainingCourses();
}

// تحميل الإدارات
function loadDepartments() {
  const departments = [...new Set(employees.map(emp => emp.department).filter(dept => dept))];
  const departmentSelect = document.getElementById('reportDepartment');
  
  if (departmentSelect) {
    departmentSelect.innerHTML = '<option value="">جميع الإدارات</option>';
    departments.forEach(dept => {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      departmentSelect.appendChild(option);
    });
  }
}

// تنسيق التاريخ الميلادي
function formatDate(dateString) {
  if (typeof DateUtils !== 'undefined') {
    return DateUtils.formatDateFromDatabase(dateString);
  }
  return '';
}

// تحويل التاريخ لتنسيق input date
function formatDateForInput(dateString) {
  if (typeof DateUtils !== 'undefined') {
    return DateUtils.formatDateForInput(dateString);
  }
  return '';
}

// تصدير بيانات التدريب
function exportTrainingData() {
  if (filteredCourses.length === 0) {
    alert('لا توجد بيانات للتصدير');
    return;
  }

  const data = filteredCourses.map(course => ({
    'كود الموظف': course.employee_code,
    'اسم الموظف': course.employee_name,
    'الإدارة': course.department || '',
    'اسم الدورة': course.course_name,
    'تاريخ الدورة': formatDate(course.course_date),
    'مدة الدورة': course.course_duration + ' يوم',
    'نوع التدريب': course.training_type,
    'التكلفة': course.course_cost + ' جنيه',
    'ملاحظات': course.notes || ''
  }));

  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'الدورات التدريبية');

  const fileName = `الدورات_التدريبية_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(wb, fileName);
}

// إنشاء تقرير التدريب
async function generateTrainingReport() {
  const department = document.getElementById('reportDepartment').value;
  const trainingType = document.getElementById('reportTrainingType').value;
  const startDate = document.getElementById('reportStartDate').value;
  const endDate = document.getElementById('reportEndDate').value;

  let reportData = [...trainingCourses];

  // تطبيق الفلاتر
  if (department) {
    reportData = reportData.filter(course => course.department === department);
  }

  if (trainingType) {
    reportData = reportData.filter(course => course.training_type === trainingType);
  }

  if (startDate) {
    const filterStartDate = new Date(startDate);
    filterStartDate.setHours(0, 0, 0, 0);
    reportData = reportData.filter(course => {
      const courseDate = new Date(course.course_date);
      courseDate.setHours(0, 0, 0, 0);
      return courseDate >= filterStartDate;
    });
  }

  if (endDate) {
    const filterEndDate = new Date(endDate);
    filterEndDate.setHours(23, 59, 59, 999);
    reportData = reportData.filter(course => {
      const courseDate = new Date(course.course_date);
      courseDate.setHours(0, 0, 0, 0);
      return courseDate <= filterEndDate;
    });
  }

  // حساب الإحصائيات
  const totalCourses = reportData.length;
  const totalCost = reportData.reduce((sum, course) => sum + parseFloat(course.course_cost || 0), 0);
  const avgDuration = totalCourses > 0 ?
    (reportData.reduce((sum, course) => sum + parseInt(course.course_duration || 0), 0) / totalCourses).toFixed(1) : 0;

  // تحديث ملخص التقرير
  document.getElementById('totalCourses').textContent = totalCourses;
  document.getElementById('totalCost').textContent = totalCost.toFixed(2) + ' جنيه';
  document.getElementById('avgDuration').textContent = avgDuration + ' يوم';

  // عرض بيانات التقرير
  displayReportData(reportData);
}

// عرض بيانات التقرير
function displayReportData(data) {
  const tableBody = document.querySelector('#report-table tbody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  data.forEach(course => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${course.employee_code}</td>
      <td>${course.employee_name}</td>
      <td>${course.department || ''}</td>
      <td>${course.course_name}</td>
      <td>${formatDate(course.course_date)}</td>
      <td>${course.course_duration} يوم</td>
      <td>${course.training_type}</td>
      <td>${course.course_cost} جنيه</td>
    `;
    tableBody.appendChild(row);
  });
}

// تصدير تقرير التدريب
function exportTrainingReport() {
  const reportTable = document.getElementById('report-table');
  const reportData = [];

  const rows = reportTable.querySelectorAll('tbody tr');
  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length > 0) {
      reportData.push({
        'كود الموظف': cells[0].textContent,
        'اسم الموظف': cells[1].textContent,
        'الإدارة': cells[2].textContent,
        'اسم الدورة': cells[3].textContent,
        'تاريخ الدورة': cells[4].textContent,
        'مدة الدورة': cells[5].textContent,
        'نوع التدريب': cells[6].textContent,
        'التكلفة': cells[7].textContent
      });
    }
  });

  if (reportData.length === 0) {
    alert('لا توجد بيانات في التقرير للتصدير');
    return;
  }

  // إضافة ملخص التقرير
  const summary = [
    { 'البيان': 'إجمالي الدورات', 'القيمة': document.getElementById('totalCourses').textContent },
    { 'البيان': 'إجمالي التكلفة', 'القيمة': document.getElementById('totalCost').textContent },
    { 'البيان': 'متوسط مدة الدورة', 'القيمة': document.getElementById('avgDuration').textContent },
    { 'البيان': '', 'القيمة': '' } // فاصل
  ];

  const ws1 = XLSX.utils.json_to_sheet(summary);
  const ws2 = XLSX.utils.json_to_sheet(reportData);

  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws1, 'ملخص التقرير');
  XLSX.utils.book_append_sheet(wb, ws2, 'تفاصيل التقرير');

  const fileName = `تقرير_التدريب_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(wb, fileName);
}

// إعداد البحث المباشر للدورات التدريبية
function setupTrainingFilters() {
  const filterInputs = ['filterTrainingEmployeeCode', 'filterTrainingEmployeeName', 'filterTrainingFromDate', 'filterTrainingToDate'];

  filterInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', function() {
        clearTimeout(this.filterTimeout);
        this.filterTimeout = setTimeout(() => {
          applyTrainingFilters();
        }, 300);
      });
    }
  });

  // زر مسح الفلاتر
  const clearBtn = document.getElementById('clearTrainingFiltersBtn');
  if (clearBtn) {
    clearBtn.addEventListener('click', clearTrainingFilters);
  }
}

// تطبيق فلاتر الدورات التدريبية
function applyTrainingFilters() {
  const employeeCode = document.getElementById('filterTrainingEmployeeCode')?.value.trim() || '';
  const employeeName = document.getElementById('filterTrainingEmployeeName')?.value.trim() || '';
  const fromDate = document.getElementById('filterTrainingFromDate')?.value || '';
  const toDate = document.getElementById('filterTrainingToDate')?.value || '';

  const tableBody = document.querySelector('#training-table tbody');
  if (!tableBody) return;

  const rows = tableBody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length === 0) return;

    // استخراج البيانات من الخلايا
    const rowEmployeeCode = cells[0]?.textContent?.trim() || '';
    const rowEmployeeName = cells[1]?.textContent?.trim() || '';
    const rowStartDate = cells[4]?.textContent?.trim() || '';
    const rowEndDate = cells[5]?.textContent?.trim() || '';

    let showRow = true;

    // فلترة بالكود
    if (employeeCode && !rowEmployeeCode.includes(employeeCode)) {
      showRow = false;
    }

    // فلترة بالاسم
    if (employeeName && !rowEmployeeName.toLowerCase().includes(employeeName.toLowerCase())) {
      showRow = false;
    }

    // فلترة بالتاريخ من
    if (fromDate && rowStartDate) {
      const rowDateObj = new Date(rowStartDate);
      const fromDateObj = new Date(fromDate);
      if (rowDateObj < fromDateObj) {
        showRow = false;
      }
    }

    // فلترة بالتاريخ إلى
    if (toDate && rowEndDate) {
      const rowDateObj = new Date(rowEndDate);
      const toDateObj = new Date(toDate);
      if (rowDateObj > toDateObj) {
        showRow = false;
      }
    }

    // إظهار أو إخفاء الصف
    row.style.display = showRow ? '' : 'none';
  });
}

// مسح فلاتر الدورات التدريبية
function clearTrainingFilters() {
  document.getElementById('filterTrainingEmployeeCode').value = '';
  document.getElementById('filterTrainingEmployeeName').value = '';
  document.getElementById('filterTrainingFromDate').value = '';
  document.getElementById('filterTrainingToDate').value = '';

  // إظهار جميع الصفوف
  const tableBody = document.querySelector('#training-table tbody');
  if (tableBody) {
    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
      row.style.display = '';
    });
  }
}

// إضافة إعداد البحث المباشر إلى setupEventListeners
const originalSetupEventListeners = setupEventListeners;
setupEventListeners = function() {
  originalSetupEventListeners();
  setupTrainingFilters();
};