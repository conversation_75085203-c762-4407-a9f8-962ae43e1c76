const express = require("express");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { logAction, createEditMessage } = require('../activityLogger');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

const router = express.Router();

// الحصول على جميع التقييمات
router.get('/', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(`
      SELECT e.*, emp.full_name as employee_name, emp.department
      FROM evaluations e
      LEFT JOIN employees emp ON e.employee_code = emp.code
      ORDER BY e.created_at DESC, e.id DESC
    `);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب التقييمات:', error);
    res.status(500).json({ error: 'فشل في جلب التقييمات' });
  }
});

// الحصول على تقييم محدد
router.get('/:id', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const { id } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT e.*, emp.full_name as employee_name, emp.department
      FROM evaluations e
      LEFT JOIN employees emp ON e.employee_code = emp.code
      WHERE e.id = ?
    `, [id]);
    
    if (rows.length === 0) {
      return res.status(404).json({ error: 'التقييم غير موجود' });
    }
    
    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب التقييم:', error);
    res.status(500).json({ error: 'فشل في جلب التقييم' });
  }
});

// إضافة تقييم جديد
router.post('/', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const {
      employee_code,
      evaluation_type,
      start_date,
      end_date,
      score,
      notes
    } = req.body;

    if (!employee_code || !evaluation_type || !start_date || !end_date || !score) {
      return res.status(400).json({ error: 'كود الموظف ونوع التقييم وتاريخ البداية والنهاية والدرجة مطلوبة' });
    }

    // التحقق من صحة نوع التقييم
    const validTypes = ['شهري', 'ربع سنوي'];
    if (!validTypes.includes(evaluation_type)) {
      return res.status(400).json({ error: 'نوع التقييم يجب أن يكون شهري أو ربع سنوي' });
    }
    
    // الحصول على بيانات الموظف
    const [employeeData] = await pool.promise().query(
      "SELECT full_name, department FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeData.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    const employee_name = employeeData[0].full_name;
    const department = employeeData[0].department;

    const [result] = await pool.promise().query(
      `INSERT INTO evaluations (
        employee_code, employee_name, department, evaluation_type,
        start_date, end_date, score, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        employee_code, employee_name, department, evaluation_type,
        start_date, end_date, score, notes
      ]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'evaluations',
      record_id: result.insertId.toString(),
      message: `تم إضافة تقييم ${evaluation_type} للموظف: ${employee_name} (كود: ${employee_code}) - القسم: ${department} - الدرجة: ${score} - الفترة: من ${start_date} إلى ${end_date}`
    });

    res.status(201).json({
      id: result.insertId,
      employee_code,
      employee_name,
      department,
      evaluation_type,
      start_date,
      end_date,
      score,
      message: 'تم إضافة التقييم بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إضافة التقييم:', error);
    res.status(500).json({ error: 'فشل في إضافة التقييم' });
  }
});

// تحديث تقييم
router.put('/:id', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const { id } = req.params;
    // تنظيف البيانات المرسلة من العميل
    const updateData = cleanUpdateData(req.body);

    // التحقق من وجود التقييم والحصول على البيانات القديمة
    const [existingEvaluation] = await pool.promise().query(
      "SELECT * FROM evaluations WHERE id = ?",
      [id]
    );

    if (existingEvaluation.length === 0) {
      return res.status(404).json({ error: 'التقييم غير موجود' });
    }

    const oldData = existingEvaluation[0];

    // إزالة الحقول غير المسموح بتحديثها
    delete updateData.id;
    delete updateData.created_at;
    delete updateData.updated_at;

    // فلترة الحقول المسموحة فقط حسب الجدول الفعلي
    const allowedFields = [
      'employee_code', 'employee_name', 'department', 'evaluation_type',
      'start_date', 'end_date', 'score', 'notes'
    ];

    // إزالة أي حقول غير مسموحة
    Object.keys(updateData).forEach(key => {
      if (!allowedFields.includes(key)) {
        delete updateData[key];
      }
    });

    // التحقق من صحة نوع التقييم إذا تم تحديثه
    if (updateData.evaluation_type) {
      const validTypes = ['شهري', 'ربع سنوي'];
      if (!validTypes.includes(updateData.evaluation_type)) {
        return res.status(400).json({ error: 'نوع التقييم يجب أن يكون شهري أو ربع سنوي' });
      }
    }

    // إذا تم تغيير كود الموظف، تحديث اسم الموظف والقسم
    if (updateData.employee_code && updateData.employee_code !== oldData.employee_code) {
      const [employeeRows] = await pool.promise().query(
        "SELECT full_name, department FROM employees WHERE code = ?",
        [updateData.employee_code]
      );

      if (employeeRows.length > 0) {
        updateData.employee_name = employeeRows[0].full_name;
        updateData.department = employeeRows[0].department;
      }
    }

    // قائمة حقول التاريخ التي تحتاج معالجة خاصة
    const dateFields = [
      'start_date', 'end_date'
    ];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    dateFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        if (updateData[field] === '' || updateData[field] === null || updateData[field] === undefined) {
          updateData[field] = null;
        }
      }
    });

    // تحضير البيانات للتحديث
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    values.push(id);

    await pool.promise().query(
      `UPDATE evaluations SET ${setClause} WHERE id = ?`,
      values
    );

    // تسجيل النشاط
    const newData = { ...oldData, ...updateData };

    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'القسم',
      evaluation_type: 'نوع التقييم',
      start_date: 'تاريخ البداية',
      end_date: 'تاريخ النهاية',
      score: 'الدرجة',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `تقييم الموظف: ${newData.employee_name}`,
      oldData,
      newData,
      fieldLabels
    );

    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'evaluations',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث التقييم بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث التقييم:', error);
    res.status(500).json({ error: 'فشل في تحديث التقييم' });
  }
});

// حذف تقييم
router.delete('/:id', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على بيانات التقييم قبل الحذف
    const [evaluationData] = await pool.promise().query(
      "SELECT * FROM evaluations WHERE id = ?",
      [id]
    );

    if (evaluationData.length === 0) {
      return res.status(404).json({ error: 'التقييم غير موجود' });
    }

    const evaluation = evaluationData[0];

    const [result] = await pool.promise().query(
      "DELETE FROM evaluations WHERE id = ?",
      [id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'evaluations',
      record_id: id.toString(),
      message: `تم حذف تقييم ${evaluation.evaluation_type} للموظف: ${evaluation.employee_name} (كود: ${evaluation.employee_code}) - القسم: ${evaluation.department} - الدرجة: ${evaluation.score} - الفترة: من ${evaluation.start_date} إلى ${evaluation.end_date}`
    });

    res.json({ message: 'تم حذف التقييم بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف التقييم:', error);
    res.status(500).json({ error: 'فشل في حذف التقييم' });
  }
});

// الحصول على تقييمات موظف محدد
router.get('/employee/:employee_code', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const { employee_code } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT e.*, emp.full_name as employee_name, emp.department
      FROM evaluations e
      LEFT JOIN employees emp ON e.employee_code = emp.code
      WHERE e.employee_code = ?
      ORDER BY e.created_at DESC, e.id DESC
    `, [employee_code]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب تقييمات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب تقييمات الموظف' });
  }
});

// الحصول على إحصائيات التقييمات
router.get('/statistics/overview', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const { start_date, end_date } = req.query;
    
    let dateFilter = "";
    const params = [];
    
    if (start_date && end_date) {
      dateFilter = " WHERE start_date BETWEEN ? AND ?";
      params.push(start_date, end_date);
    }
    
    // إجمالي التقييمات
    const [totalResult] = await pool.promise().query(
      `SELECT COUNT(*) as total_count, AVG(overall_score) as average_score FROM evaluations${dateFilter}`,
      params
    );
    
    // توزيع الدرجات
    const [scoreDistribution] = await pool.promise().query(
      `SELECT 
         CASE 
           WHEN overall_score >= 90 THEN 'ممتاز (90-100)'
           WHEN overall_score >= 80 THEN 'جيد جداً (80-89)'
           WHEN overall_score >= 70 THEN 'جيد (70-79)'
           WHEN overall_score >= 60 THEN 'مقبول (60-69)'
           ELSE 'ضعيف (أقل من 60)'
         END as score_range,
         COUNT(*) as count
       FROM evaluations${dateFilter}
       GROUP BY score_range
       ORDER BY MIN(overall_score) DESC`,
      params
    );
    
    // أفضل الموظفين
    const [topPerformers] = await pool.promise().query(
      `SELECT e.employee_code, emp.full_name as employee_name, AVG(e.overall_score) as average_score, COUNT(*) as evaluation_count
       FROM evaluations e
       LEFT JOIN employees emp ON e.employee_code = emp.code${dateFilter}
       GROUP BY e.employee_code, emp.full_name
       HAVING evaluation_count > 0
       ORDER BY average_score DESC
       LIMIT 10`,
      params
    );
    
    res.json({
      total: totalResult[0],
      score_distribution: scoreDistribution,
      top_performers: topPerformers
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات التقييمات:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات التقييمات' });
  }
});

// البحث في التقييمات
router.get('/search', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const { 
      employee_code, 
      start_date, 
      end_date, 
      min_score, 
      max_score,
      evaluator,
      evaluation_period
    } = req.query;
    
    let query = `
      SELECT e.*, emp.full_name as employee_name, emp.department
      FROM evaluations e
      LEFT JOIN employees emp ON e.employee_code = emp.code
      WHERE 1=1
    `;
    const params = [];
    
    if (employee_code) {
      query += " AND e.employee_code = ?";
      params.push(employee_code);
    }
    
    if (start_date) {
      query += " AND e.start_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND e.end_date <= ?";
      params.push(end_date);
    }
    
    if (min_score) {
      query += " AND e.overall_score >= ?";
      params.push(min_score);
    }
    
    if (max_score) {
      query += " AND e.overall_score <= ?";
      params.push(max_score);
    }
    
    if (evaluator) {
      query += " AND e.evaluator LIKE ?";
      params.push(`%${evaluator}%`);
    }
    
    if (evaluation_period) {
      query += " AND e.evaluation_period = ?";
      params.push(evaluation_period);
    }
    
    query += " ORDER BY e.created_at DESC, e.id DESC";
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في التقييمات:', error);
    res.status(500).json({ error: 'فشل في البحث في التقييمات' });
  }
});

module.exports = router;