document.addEventListener('DOMContentLoaded', function() {
  // متغيرات عامة
  let employees = [];
  let departments = [];
  let resignations = [];
  let editId = null;

  // عناصر DOM
  const tabBtns = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');
  
  // عناصر النموذج
  const employeeSearch = document.getElementById('resignationEmployeeSearch');
  const employeeCode = document.getElementById('resignationEmployeeCode');
  const employeeName = document.getElementById('resignationEmployeeName');
  const employeeDepartment = document.getElementById('resignationEmployeeDepartment');
  const resignationReason = document.getElementById('resignationReason');
  const resignationDate = document.getElementById('resignationDate');
  const workEndDate = document.getElementById('workEndDate');
  const resignationRecommendation = document.getElementById('resignationRecommendation');
  const resignationNotes = document.getElementById('resignationNotes');
  
  // أزرار النموذج
  const saveBtn = document.getElementById('saveResignation');
  const resetBtn = document.getElementById('resetResignationForm');
  
  // عناصر الجدول
  const resignationTableBody = document.getElementById('resignationTableBody');
  const searchResignation = document.getElementById('searchResignation');
  const exportBtn = document.getElementById('exportResignationsBtn');
  
  // عناصر التقارير
  const reportStartDate = document.getElementById('reportStartDate');
  const reportEndDate = document.getElementById('reportEndDate');
  const reportDepartment = document.getElementById('reportDepartment');
  const reportReason = document.getElementById('reportReason');
  const reportRecommendation = document.getElementById('reportRecommendation');
  const applyFiltersBtn = document.getElementById('applyReportFilters');
  const resetFiltersBtn = document.getElementById('resetReportFilters');
  const reportTableBody = document.querySelector('#resignation-report-table tbody');
  const searchReportTable = document.getElementById('searchReportTable');
  const exportReportBtn = document.getElementById('exportReportTableBtn');
  const printReportBtn = document.getElementById('printReportBtn');
  
  // عناصر النافذة المنبثقة
  const editModal = document.getElementById('editResignationModal');
  const closeModalBtn = document.getElementById('closeEditResignationModal');
  const updateBtn = document.getElementById('updateResignationBtn');

  // إعدادات API
  const API_URL = 'http://localhost:5500/api';

  // تحميل البيانات عند بدء التشغيل
  loadData();
  setupEventListeners();


  // تطبيق الصلاحيات على الواجهة بعد تأخير للتأكد من تحميل permissions.js
  setTimeout(() => {
    applyPermissions();
  }, 200);



  // تحميل البيانات
  async function loadData() {
    try {
      console.log(`محاولة تحميل البيانات من: ${API_URL}`);
      
      try {
        await loadDepartments();
        console.log('تم تحميل الإدارات بنجاح');
      } catch (deptError) {
        console.error('فشل في تحميل الإدارات:', deptError);
        alert('فشل في تحميل الإدارات: ' + deptError.message);
      }
      
      try {
        await loadEmployees();
        console.log('تم تحميل الموظفين بنجاح');
      } catch (empError) {
        console.error('فشل في تحميل الموظفين:', empError);
        alert('فشل في تحميل الموظفين: ' + empError.message);
      }
      
      try {
        await loadResignations();
        console.log('تم تحميل الاستقالات بنجاح');
      } catch (resError) {
        console.error('فشل في تحميل الاستقالات:', resError);
        alert('فشل في تحميل الاستقالات: ' + resError.message);
      }
      
      return true;
    } catch (error) {
      console.error('فشل في تحميل البيانات:', error);
      alert('فشل في تحميل البيانات: ' + error.message);
      return false;
    }
  }

  // تحميل الإدارات
  async function loadDepartments() {
    try {
      console.log(`جاري تحميل الإدارات من: ${API_URL}/departments`);
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/departments`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('استجابة تحميل الإدارات:', response.status);
      
      if (response.ok) {
        departments = await response.json();
        console.log('تم تحميل الإدارات بنجاح:', departments);
        populateDepartmentSelects();
      } else {
        console.error(`فشل في تحميل الإدارات: ${response.status} ${response.statusText}`);
        throw new Error(`فشل في تحميل الإدارات: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('خطأ في تحميل الإدارات:', error);
      throw error;
    }
  }

  // تعبئة قوائم الإدارات
  function populateDepartmentSelects() {
    reportDepartment.innerHTML = '<option value="">كل الإدارات</option>';
    departments.forEach(dept => {
      const option = document.createElement('option');
      option.value = dept;
      option.textContent = dept;
      reportDepartment.appendChild(option);
    });
  }

  // تحميل الموظفين
  async function loadEmployees() {
    try {
      console.log(`جاري تحميل الموظفين من: ${API_URL}/employees`);
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/employees`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('استجابة تحميل الموظفين:', response.status);
      
      if (response.ok) {
        employees = await response.json();
        console.log('تم تحميل الموظفين بنجاح، عدد الموظفين:', employees.length); 
      } else {
        console.error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
        throw new Error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات الموظفين:', error);
      throw error;
    }
  }

  // البحث عن الموظف
  function handleEmployeeSearch(searchTerm, datalistId) {
    const datalist = document.getElementById(datalistId);
    datalist.innerHTML = '';
    
    console.log(`البحث عن الموظف: ${searchTerm}, عدد الموظفين المتاحين: ${employees.length}`);
    
    if (!searchTerm || searchTerm.trim() === '') {
      // مسح الحقول عند مسح البحث
      employeeCode.value = '';
      employeeName.value = '';
      employeeDepartment.value = '';
      return;
    }
    
    const searchTermLower = searchTerm.toLowerCase().trim();
    
    // البحث في الموظفين
    const filteredEmployees = employees.filter(emp => {
      const fullName = emp.full_name ? String(emp.full_name).toLowerCase() : '';
      const code = emp.code ? String(emp.code).toLowerCase() : '';
      const department = emp.department ? String(emp.department).toLowerCase() : '';
      
      return fullName.includes(searchTermLower) || 
             code.includes(searchTermLower) || 
             department.includes(searchTermLower);
    });
    
    console.log(`نتائج البحث: ${filteredEmployees.length} موظف`);
    
    // البحث عن تطابق مباشر أولاً
    const exactCodeMatch = employees.find(emp => 
      emp.code && String(emp.code).toLowerCase() === searchTermLower
    );
    const exactNameMatch = employees.find(emp => 
      emp.full_name && String(emp.full_name).toLowerCase() === searchTermLower
    );
    
    // إذا وُجد تطابق مباشر، املأ الحقول
    if (exactCodeMatch) {
      fillEmployeeFields(exactCodeMatch);
    } else if (exactNameMatch) {
      fillEmployeeFields(exactNameMatch);
    }
    // إذا كان هناك موظف واحد فقط في النتائج، املأ الحقول
    else if (filteredEmployees.length === 1) {
      fillEmployeeFields(filteredEmployees[0]);
    }
    
    if (filteredEmployees.length > 0) {
      // عرض أول 15 نتيجة فقط لتحسين الأداء
      filteredEmployees.slice(0, 15).forEach(emp => {
        const option = document.createElement('option');
        option.value = `${emp.code} - ${emp.full_name}`;
        datalist.appendChild(option);
      });
    } else {
      // إضافة رسالة إذا لم يتم العثور على نتائج
      const option = document.createElement('option');
      option.value = "لا توجد نتائج مطابقة";
      datalist.appendChild(option);
    }
  }

  // وظيفة مساعدة لملء حقول الموظف
  function fillEmployeeFields(employee) {
    if (employee) {
      employeeCode.value = employee.code || '';
      employeeName.value = employee.full_name || '';
      employeeDepartment.value = employee.department || '';
    }
  }

  // اختيار الموظف
  function handleEmployeeSelection(value) {
    if (!value) return;

    const codeMatch = value.match(/^(\S+)\s*-/);
    if (!codeMatch) return;

    const code = codeMatch[1];
    const employee = employees.find(emp => emp.code == code);

    if (employee) {
      employeeCode.value = employee.code;
      employeeName.value = employee.full_name;
      employeeDepartment.value = employee.department || '';
    }
  }

  // تحميل الاستقالات
  async function loadResignations() {
    try {
      console.log(`جاري تحميل الاستقالات من: ${API_URL}/resignations`);
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/resignations`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('استجابة تحميل الاستقالات:', response.status);

      if (response.ok) {
        resignations = await response.json();
        console.log('تم تحميل الاستقالات بنجاح، عدد الاستقالات:', resignations.length);
        displayResignations();
        displayReports();
        // إعادة تطبيق الصلاحيات بعد تحميل البيانات
        setTimeout(() => {
          applyPermissions();
        }, 100);
      } else {
        console.error(`فشل في تحميل الاستقالات: ${response.status} ${response.statusText}`);
        throw new Error(`فشل في تحميل الاستقالات: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات الاستقالات:', error);
      throw error;
    }
  }

  // فحص الصلاحيات
  function hasPermission(permission) {
    try {
      const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
      const result = permissions[permission] === true;
      console.log(`[Resignation] hasPermission(${permission}) = ${result}`, permissions);
      return result;
    } catch (error) {
      console.error('خطأ في قراءة الصلاحيات:', error);
      return false;
    }
  }

  // تطبيق الصلاحيات على واجهة المستخدم
  function applyPermissions() {
    // إخفاء/إظهار نموذج الإضافة
    const addForm = document.querySelector('.resignation-form');
    if (addForm) {
      if (!hasPermission('add_resignation')) {
        addForm.style.display = 'none';
        // إضافة رسالة تنبيه
        const noPermissionMsg = document.createElement('div');
        noPermissionMsg.className = 'no-permission-message';
        noPermissionMsg.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">ليس لديك صلاحية لإضافة الاستقالات</p>';
        addForm.parentNode.insertBefore(noPermissionMsg, addForm);
      }
    }

    // إخفاء/إظهار أزرار التصدير حسب صلاحية التقارير
    const exportButtons = document.querySelectorAll('.export-btn, #exportBtn, #exportReportBtn');
    exportButtons.forEach(btn => {
      if (!hasPermission('view_resignation_reports')) {
        btn.style.display = 'none';
      }
    });

    // إخفاء تبويب التقارير إذا لم تكن هناك صلاحية
    const reportsTab = document.querySelector('[data-tab="reports"]');
    if (reportsTab && !hasPermission('view_resignation_reports')) {
      reportsTab.style.display = 'none';
    }
  }

  // عرض الاستقالات في الجدول
  function displayResignations(filteredResignations = null) {
    const dataToShow = filteredResignations || resignations;
    resignationTableBody.innerHTML = '';

    // فحص صلاحيات التعديل والحذف
    const canEdit = hasPermission('edit_resignation');
    const canDelete = hasPermission('delete_resignation');

    console.log(`[Resignation] displayResignations: canEdit=${canEdit}, canDelete=${canDelete}`);

    // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
    const sortedResignations = [...dataToShow].sort((a, b) => {
      // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
      const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
      const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
      return bTime - aTime; // الأحدث أولاً
    });

    sortedResignations.forEach(async (resignation, index) => {
      const row = document.createElement('tr');

      // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
      if (index === 0) {
        row.style.backgroundColor = '#e8f5e8';
        row.style.border = '2px solid #4CAF50';
      }

      // التحقق من حالة الموظف
      let employeeStatus = 'غير معروف';
      let isResigned = false;
      try {
        const empResponse = await fetch(`${API_URL}/employees/${resignation.employee_code}`);
        if (empResponse.ok) {
          const employee = await empResponse.json();
          employeeStatus = employee.status === 'مستقيل' ? 'مستقيل' : 'نشط';
          isResigned = employee.status === 'مستقيل';
        }
      } catch (error) {
        console.error('خطأ في جلب حالة الموظف:', error);
      }

      // إنشاء أزرار الإجراءات حسب الصلاحيات
      let actionsHTML = '';
      if (canEdit || canDelete) {
        actionsHTML = '<td class="actions-column">';
        if (canEdit) {
          actionsHTML += `<button class="edit-btn" onclick="editResignation(${resignation.id})">تعديل</button>`;
        }
        if (canDelete) {
          actionsHTML += `<button class="delete-btn" onclick="deleteResignation(${resignation.id})">حذف</button>`;
        }

        actionsHTML += '</td>';
      } else {
        actionsHTML = '<td class="actions-column">لا توجد إجراءات متاحة</td>';
      }

      // إضافة فئة CSS للموظفين المستقيلين
      if (isResigned) {
        row.classList.add('resigned-employee');
      }

      row.innerHTML = `
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${resignation.employee_code || ''}</td>
        <td class="employee-name" style="${index === 0 ? 'font-weight: bold;' : ''}">${resignation.employee_name || ''}</td>
        <td class="department-column" style="${index === 0 ? 'font-weight: bold;' : ''}">${resignation.department || ''}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${resignation.reason || ''}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatDate(resignation.resignation_date)}</td>
        <td class="work-end-date-column" style="${index === 0 ? 'font-weight: bold;' : ''}">${resignation.work_end_date ? formatDate(resignation.work_end_date) : '-'}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${resignation.recommendation || ''}</td>
        <td class="notes-column" style="${index === 0 ? 'font-weight: bold;' : ''}">${resignation.notes || ''}</td>
        <td style="${index === 0 ? 'font-weight: bold;' : ''}">${isResigned ? '<span class="resigned-status-badge">مستقيل</span>' : '<span style="color: green;">نشط</span>'}</td>
        ${actionsHTML}
        <td><button class="view-employee-btn" onclick="viewEmployeeDetails('${resignation.employee_code}')">عرض</button></td>
      `;
      resignationTableBody.appendChild(row);
    });
  }

  // تنسيق التاريخ
  function formatDate(dateString) {
    if (typeof DateUtils !== 'undefined') {
      return DateUtils.formatDateFromDatabase(dateString);
    }
    return '';
  }

  // حفظ الاستقالة
  async function saveResignation() {
    try {
      // فحص صلاحية الإضافة
      if (!hasPermission('add_resignation')) {
        alert('ليس لديك صلاحية لإضافة الاستقالات');
        return;
      }

      // التحقق من صحة البيانات
      if (!employeeCode.value || !resignationReason.value || !resignationDate.value || !resignationRecommendation.value) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      // التحقق من أن تاريخ ترك العمل لا يكون قبل تاريخ الاستقالة
      if (workEndDate.value && workEndDate.value < resignationDate.value) {
        alert('تاريخ ترك العمل لا يمكن أن يكون قبل تاريخ الاستقالة');
        return;
      }

      const resignationData = {
        employee_code: employeeCode.value,
        employee_name: employeeName.value,
        department: employeeDepartment.value,
        reason: resignationReason.value,
        resignation_date: resignationDate.value,
        work_end_date: workEndDate.value.trim() !== '' ? workEndDate.value : null,
        recommendation: resignationRecommendation.value,
        notes: resignationNotes.value || ''
      };



      console.log('بيانات الاستقالة المرسلة:', resignationData);

      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/resignations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(resignationData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('تم حفظ الاستقالة بنجاح:', result);
        alert('تم حفظ الاستقالة بنجاح');
        resetForm();
        await loadResignations();
      } else {
        const errorData = await response.json();
        console.error('فشل في حفظ الاستقالة:', errorData);
        alert('فشل في حفظ الاستقالة: ' + (errorData.error || 'خطأ غير معروف'));
      }
    } catch (error) {
      console.error('خطأ في حفظ الاستقالة:', error);
      alert('خطأ في حفظ الاستقالة: ' + error.message);
    }
  }

  // إعادة تعيين النموذج
  function resetForm() {
    employeeSearch.value = '';
    employeeCode.value = '';
    employeeName.value = '';
    employeeDepartment.value = '';
    resignationReason.value = '';
    resignationDate.value = '';
    workEndDate.value = '';
    resignationRecommendation.value = '';
    resignationNotes.value = '';
    editId = null;
  }

  // تعديل الاستقالة
  window.editResignation = function(id) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_resignation')) {
      alert('ليس لديك صلاحية لتعديل الاستقالات');
      return;
    }

    const resignation = resignations.find(r => r.id === id);
    if (!resignation) {
      alert('لم يتم العثور على الاستقالة');
      return;
    }

    // ملء النافذة المنبثقة بالبيانات
    document.getElementById('editResignationId').value = resignation.id;
    document.getElementById('editResignationEmployeeCode').value = resignation.employee_code || '';
    document.getElementById('editResignationEmployeeName').value = resignation.employee_name || '';
    document.getElementById('editResignationEmployeeDepartment').value = resignation.department || '';
    document.getElementById('editResignationReason').value = resignation.reason || '';
    // استخدام DateUtils لتنسيق التاريخ بشكل صحيح
    if (typeof DateUtils !== 'undefined') {
      document.getElementById('editResignationDate').value = DateUtils.formatDateForInput(resignation.resignation_date) || '';
      document.getElementById('editWorkEndDate').value = DateUtils.formatDateForInput(resignation.work_end_date) || '';
    } else {
      document.getElementById('editResignationDate').value = formatDateForInput(resignation.resignation_date) || '';
      document.getElementById('editWorkEndDate').value = formatDateForInput(resignation.work_end_date) || '';
    }
    document.getElementById('editResignationRecommendation').value = resignation.recommendation || '';
    document.getElementById('editResignationNotes').value = resignation.notes || '';

    // عرض النافذة المنبثقة
    editModal.style.display = 'block';
  };

  // حذف الاستقالة
  window.deleteResignation = async function(id) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_resignation')) {
      alert('ليس لديك صلاحية لحذف الاستقالات');
      return;
    }

    if (!confirm('هل أنت متأكد من حذف هذه الاستقالة؟ سيتم إعادة تفعيل الموظف تلقائياً.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/resignations/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        alert(result.message || 'تم حذف الاستقالة وإعادة تفعيل الموظف بنجاح');
        await loadResignations();
      } else {
        const errorData = await response.json();
        alert('فشل في حذف الاستقالة: ' + (errorData.error || 'خطأ غير معروف'));
      }
    } catch (error) {
      console.error('خطأ في حذف الاستقالة:', error);
      alert('خطأ في حذف الاستقالة: ' + error.message);
    }
  };



  // تحديث الاستقالة
  async function updateResignation() {
    try {
      const id = document.getElementById('editResignationId').value;
      const resignationDate = document.getElementById('editResignationDate').value;
      const workEndDate = document.getElementById('editWorkEndDate').value;

      // التحقق من أن تاريخ ترك العمل لا يكون قبل تاريخ الاستقالة
      if (workEndDate && workEndDate < resignationDate) {
        alert('تاريخ ترك العمل لا يمكن أن يكون قبل تاريخ الاستقالة');
        return;
      }

      const resignationData = {
        reason: document.getElementById('editResignationReason').value,
        resignation_date: resignationDate,
        work_end_date: workEndDate && workEndDate.trim() !== '' ? workEndDate : null,
        recommendation: document.getElementById('editResignationRecommendation').value,
        notes: document.getElementById('editResignationNotes').value || ''
      };



      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/resignations/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(resignationData)
      });

      if (response.ok) {
        alert('تم تحديث الاستقالة بنجاح');
        editModal.style.display = 'none';
        await loadResignations();
      } else {
        const errorData = await response.json();
        alert('فشل في تحديث الاستقالة: ' + (errorData.error || 'خطأ غير معروف'));
      }
    } catch (error) {
      console.error('خطأ في تحديث الاستقالة:', error);
      alert('خطأ في تحديث الاستقالة: ' + error.message);
    }
  }

  // البحث في الاستقالات
  function searchResignations() {
    const searchTerm = searchResignation.value.toLowerCase().trim();

    if (!searchTerm) {
      displayResignations();
      return;
    }

    const filteredResignations = resignations.filter(resignation => {
      return (
        (resignation.employee_code && resignation.employee_code.toLowerCase().includes(searchTerm)) ||
        (resignation.employee_name && resignation.employee_name.toLowerCase().includes(searchTerm)) ||
        (resignation.department && resignation.department.toLowerCase().includes(searchTerm)) ||
        (resignation.reason && resignation.reason.toLowerCase().includes(searchTerm)) ||
        (resignation.resignation_date && resignation.resignation_date.includes(searchTerm)) ||
        (resignation.work_end_date && resignation.work_end_date.includes(searchTerm)) ||
        (resignation.recommendation && resignation.recommendation.toLowerCase().includes(searchTerm)) ||
        (resignation.notes && resignation.notes.toLowerCase().includes(searchTerm))
      );
    });

    displayResignations(filteredResignations);
  }

  // عرض التقارير
  function displayReports() {
    // حساب الإحصائيات
    const totalResignations = resignations.length;
    const recommendedCount = resignations.filter(r => r.recommendation === 'يوصى بعودته').length;
    const notRecommendedCount = resignations.filter(r => r.recommendation === 'لا يوصى بعودته').length;

    // تحديث البطاقات
    document.getElementById('totalResignationsValue').textContent = totalResignations;
    document.getElementById('recommendedResignationsValue').textContent = recommendedCount;
    document.getElementById('notRecommendedResignationsValue').textContent = notRecommendedCount;

    // عرض جدول التقارير
    displayReportTable();
  }

  // عرض جدول التقارير
  function displayReportTable(filteredData = null) {
    const dataToShow = filteredData || resignations;
    reportTableBody.innerHTML = '';

    dataToShow.forEach(resignation => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${resignation.employee_code || ''}</td>
        <td>${resignation.employee_name || ''}</td>
        <td class="department-column">${resignation.department || ''}</td>
        <td>${resignation.reason || ''}</td>
        <td>${formatDate(resignation.resignation_date)}</td>
        <td>${resignation.recommendation || ''}</td>
        <td class="notes-column">${resignation.notes || ''}</td>
      `;
      reportTableBody.appendChild(row);
    });
  }

  // تطبيق فلاتر التقارير
  function applyReportFilters() {
    let filteredData = [...resignations];

    // فلتر التاريخ
    if (reportStartDate.value) {
      const filterStartDate = new Date(reportStartDate.value);
      filterStartDate.setHours(0, 0, 0, 0);
      filteredData = filteredData.filter(r => {
        const resignationDate = new Date(r.resignation_date);
        resignationDate.setHours(0, 0, 0, 0);
        return resignationDate >= filterStartDate;
      });
    }
    if (reportEndDate.value) {
      const filterEndDate = new Date(reportEndDate.value);
      filterEndDate.setHours(23, 59, 59, 999);
      filteredData = filteredData.filter(r => {
        const resignationDate = new Date(r.resignation_date);
        resignationDate.setHours(0, 0, 0, 0);
        return resignationDate <= filterEndDate;
      });
    }

    // فلتر الإدارة
    if (reportDepartment.value) {
      filteredData = filteredData.filter(r => r.department === reportDepartment.value);
    }

    // فلتر السبب
    if (reportReason.value) {
      filteredData = filteredData.filter(r =>
        r.reason && r.reason.toLowerCase().includes(reportReason.value.toLowerCase())
      );
    }

    // فلتر التوصية
    if (reportRecommendation.value) {
      filteredData = filteredData.filter(r => r.recommendation === reportRecommendation.value);
    }

    displayReportTable(filteredData);
    updateReportStats(filteredData);
  }

  // تحديث إحصائيات التقارير
  function updateReportStats(data) {
    const totalResignations = data.length;
    const recommendedCount = data.filter(r => r.recommendation === 'يوصى بعودته').length;
    const notRecommendedCount = data.filter(r => r.recommendation === 'لا يوصى بعودته').length;

    document.getElementById('totalResignationsValue').textContent = totalResignations;
    document.getElementById('recommendedResignationsValue').textContent = recommendedCount;
    document.getElementById('notRecommendedResignationsValue').textContent = notRecommendedCount;
  }

  // إعادة تعيين فلاتر التقارير
  function resetReportFilters() {
    reportStartDate.value = '';
    reportEndDate.value = '';
    reportDepartment.value = '';
    reportReason.value = '';
    reportRecommendation.value = '';
    displayReportTable();
    updateReportStats(resignations);
  }

  // البحث في جدول التقارير
  function searchInReportTable() {
    const searchTerm = searchReportTable.value.toLowerCase().trim();

    if (!searchTerm) {
      displayReportTable();
      return;
    }

    const filteredData = resignations.filter(resignation => {
      return (
        (resignation.employee_code && resignation.employee_code.toLowerCase().includes(searchTerm)) ||
        (resignation.employee_name && resignation.employee_name.toLowerCase().includes(searchTerm)) ||
        (resignation.department && resignation.department.toLowerCase().includes(searchTerm)) ||
        (resignation.reason && resignation.reason.toLowerCase().includes(searchTerm)) ||
        (resignation.recommendation && resignation.recommendation.toLowerCase().includes(searchTerm)) ||
        (resignation.notes && resignation.notes.toLowerCase().includes(searchTerm))
      );
    });

    displayReportTable(filteredData);
  }

  // تصدير إلى Excel
  function exportToExcel(data, filename) {
    try {
      const ws = XLSX.utils.json_to_sheet(data.map(resignation => ({
        'كود الموظف': resignation.employee_code || '',
        'اسم الموظف': resignation.employee_name || '',
        'الإدارة': resignation.department || '',
        'سبب الاستقالة': resignation.reason || '',
        'تاريخ الاستقالة': formatDate(resignation.resignation_date),
        'تاريخ ترك العمل': resignation.work_end_date ? formatDate(resignation.work_end_date) : '',
        'التوصية': resignation.recommendation || '',
        'ملاحظات': resignation.notes || ''
      })));

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'الاستقالات');
      XLSX.writeFile(wb, filename);
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('فشل في تصدير البيانات: ' + error.message);
    }
  }

  // طباعة التقرير
  function printReport() {
    const printWindow = window.open('', '_blank');
    const reportTable = document.getElementById('resignation-report-table');

    printWindow.document.write(`
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>تقرير الاستقالات</title>
        <style>
          body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          th { background-color: #f2f2f2; font-weight: bold; }
          h1 { text-align: center; color: #333; }
          .print-date { text-align: center; margin-bottom: 20px; color: #666; }
        </style>
      </head>
      <body>
        <h1>تقرير الاستقالات</h1>
        <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</div>
        ${reportTable.outerHTML}
      </body>
      </html>
    `);

    printWindow.document.close();
    printWindow.print();
  }



  // إعداد مستمعي الأحداث
  function setupEventListeners() {

    // أحداث البحث عن الموظف
    if (employeeSearch) {
      employeeSearch.addEventListener('input', function() {
        handleEmployeeSearch(this.value, 'resignationEmployeeSearchSuggestions');
      });
      employeeSearch.addEventListener('change', function() {
        handleEmployeeSelection(this.value);
      });
    }

    // أحداث النموذج
    if (saveBtn) {
      saveBtn.addEventListener('click', saveResignation);
    }
    if (resetBtn) {
      resetBtn.addEventListener('click', resetForm);
    }

    // أحداث البحث في الجدول
    if (searchResignation) {
      searchResignation.addEventListener('input', searchResignations);
    }

    // أحداث التصدير
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        exportToExcel(resignations, 'الاستقالات.xlsx');
      });
    }

    // أحداث التقارير
    if (applyFiltersBtn) {
      applyFiltersBtn.addEventListener('click', applyReportFilters);
    }
    if (resetFiltersBtn) {
      resetFiltersBtn.addEventListener('click', resetReportFilters);
    }
    if (searchReportTable) {
      searchReportTable.addEventListener('input', searchInReportTable);
    }
    if (exportReportBtn) {
      exportReportBtn.addEventListener('click', () => {
        const currentData = Array.from(reportTableBody.querySelectorAll('tr')).map(row => {
          const cells = row.querySelectorAll('td');
          return {
            employee_code: cells[0]?.textContent || '',
            employee_name: cells[1]?.textContent || '',
            department: cells[2]?.textContent || '',
            reason: cells[3]?.textContent || '',
            resignation_date: cells[4]?.textContent || '',
            work_end_date: cells[5]?.textContent || '',
            recommendation: cells[6]?.textContent || '',
            notes: cells[7]?.textContent || ''
          };
        });
        exportToExcel(currentData, 'تقرير_الاستقالات.xlsx');
      });
    }
    if (printReportBtn) {
      printReportBtn.addEventListener('click', printReport);
    }

    // أحداث النافذة المنبثقة
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', () => {
        editModal.style.display = 'none';
      });
    }
    if (updateBtn) {
      updateBtn.addEventListener('click', updateResignation);
    }

    // إغلاق النافذة المنبثقة عند النقر خارجها
    window.addEventListener('click', (event) => {
      if (event.target === editModal) {
        editModal.style.display = 'none';
      }
    });
  }

  // إضافة دالة عرض بيانات الموظف
  window.viewEmployeeDetails = function(employeeCode) {
    // فتح صفحة عرض بيانات الموظف في نافذة جديدة
    window.open(`view.html?code=${employeeCode}`, '_blank');
  };

  // التحقق من التبويب المحدد من البطاقات
  setTimeout(() => {
    checkSelectedContent();
  }, 100);
});

// التحقق من المحتوى المحدد من البطاقات
function checkSelectedContent() {
  const selectedContent = localStorage.getItem('selectedResignationTab');

  if (selectedContent) {
    // حذف المحتوى المحفوظ
    localStorage.removeItem('selectedResignationTab');

    // عرض المحتوى المناسب
    showContent(selectedContent);
  } else {
    // عرض المحتوى الافتراضي (إضافة استقالة)
    showContent('add-resignation');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.classList.remove('active');
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.classList.add('active');
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-resignation') {
        pageTitle.textContent = 'إضافة استقالة';
      } else if (contentType === 'resignation-reports') {
        pageTitle.textContent = 'تقارير الاستقالات';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'resignation-reports') {
      // يمكن إضافة تحميل بيانات التقارير هنا إذا لزم الأمر
    }
  } else {
    console.error('لم يتم العثور على المحتوى:', contentType);
  }
}

// إعداد البحث المباشر للاستقالات
function setupResignationFilters() {
  const filterInputs = ['filterResignationEmployeeCode', 'filterResignationEmployeeName', 'filterResignationFromDate', 'filterResignationToDate'];

  filterInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', function() {
        clearTimeout(this.filterTimeout);
        this.filterTimeout = setTimeout(() => {
          applyResignationFilters();
        }, 300);
      });
    }
  });

  // زر مسح الفلاتر
  const clearBtn = document.getElementById('clearResignationFiltersBtn');
  if (clearBtn) {
    clearBtn.addEventListener('click', clearResignationFilters);
  }
}

// تطبيق فلاتر الاستقالات
function applyResignationFilters() {
  const employeeCode = document.getElementById('filterResignationEmployeeCode')?.value.trim() || '';
  const employeeName = document.getElementById('filterResignationEmployeeName')?.value.trim() || '';
  const fromDate = document.getElementById('filterResignationFromDate')?.value || '';
  const toDate = document.getElementById('filterResignationToDate')?.value || '';

  const tableBody = document.getElementById('resignationTableBody');
  if (!tableBody) return;

  const rows = tableBody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length === 0) return;

    // استخراج البيانات من الخلايا
    const rowEmployeeCode = cells[0]?.textContent?.trim() || '';
    const rowEmployeeName = cells[1]?.textContent?.trim() || '';
    const rowDate = cells[3]?.textContent?.trim() || '';

    let showRow = true;

    // فلترة بالكود
    if (employeeCode && !rowEmployeeCode.includes(employeeCode)) {
      showRow = false;
    }

    // فلترة بالاسم
    if (employeeName && !rowEmployeeName.toLowerCase().includes(employeeName.toLowerCase())) {
      showRow = false;
    }

    // فلترة بالتاريخ من
    if (fromDate && rowDate) {
      const rowDateObj = new Date(rowDate);
      const fromDateObj = new Date(fromDate);
      if (rowDateObj < fromDateObj) {
        showRow = false;
      }
    }

    // فلترة بالتاريخ إلى
    if (toDate && rowDate) {
      const rowDateObj = new Date(rowDate);
      const toDateObj = new Date(toDate);
      if (rowDateObj > toDateObj) {
        showRow = false;
      }
    }

    // إظهار أو إخفاء الصف
    row.style.display = showRow ? '' : 'none';
  });
}

// مسح فلاتر الاستقالات
function clearResignationFilters() {
  document.getElementById('filterResignationEmployeeCode').value = '';
  document.getElementById('filterResignationEmployeeName').value = '';
  document.getElementById('filterResignationFromDate').value = '';
  document.getElementById('filterResignationToDate').value = '';

  // إظهار جميع الصفوف
  const tableBody = document.getElementById('resignationTableBody');
  if (tableBody) {
    const rows = tableBody.querySelectorAll('tr');
    rows.forEach(row => {
      row.style.display = '';
    });
  }
}

// إضافة إعداد البحث المباشر إلى setupEventListeners
const originalSetupEventListeners = setupEventListeners;
setupEventListeners = function() {
  originalSetupEventListeners();
  setupResignationFilters();
};
