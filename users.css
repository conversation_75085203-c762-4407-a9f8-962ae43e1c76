/* تنسيقات صفحة إدارة المستخدمين المحسنة */
/* تم حذف تنسيقات main-content - يتم التحكم بها من sidebar.css فقط */

/* تحسين تنسيق عناصر التحكم بالسيرفر */
.server-controls {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(25, 118, 210, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.status-btn {
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 700;
  cursor: default;
  border: 2px solid transparent;
  font-size: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.status-btn.connected {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.status-btn.connected::before {
  content: "🟢";
  font-size: 16px;
}

.status-btn.disconnected {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.status-btn.disconnected::before {
  content: "🔴";
  font-size: 16px;
}

.control-btn {
  padding: 12px 20px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border: none;
  cursor: pointer;
  font-weight: 700;
  font-size: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.control-btn::before {
  content: "⚡";
  font-size: 16px;
}

.control-btn:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-dark));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(25, 118, 210, 0.4);
}

.control-btn:disabled {
  background: linear-gradient(135deg, #ccc, #bbb);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-btn:disabled::before {
  content: "⏸️";
}

.users-table-container {
  margin-top: 20px;
  overflow-x: auto;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  padding: 15px;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: 12px 15px;
  text-align: right;
  border-bottom: 1px solid #e9ecef;
}

.users-table th {
  background-color: var(--primary-color);
  font-weight: 600;
  color: white;
  font-size: 14px;
}

.users-table tbody tr:hover {
  background-color: #f8f9fa;
}

.users-table td {
  font-size: 14px;
  color: #333;
}

.actions-bar {
  margin: 20px 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.add-user-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: background-color 0.2s ease;
}

.add-user-btn:hover {
  background-color: #45a049;
}

/* تنسيقات أزرار الإجراءات البسيطة */
.action-btn {
  border: none;
  cursor: pointer;
  margin: 0 3px;
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  font-weight: 500;
  color: white;
}

.edit-btn {
  background-color: #2196F3;
}

.edit-btn:hover {
  background-color: #1976D2;
}

.delete-btn {
  background-color: #f44336;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

/* تنسيقات النافذة المنبثقة البسيطة */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: auto;
  transition: opacity 0.3s ease;
}

.modal-content {
  background-color: #ffffff;
  margin: 1% auto;
  padding: 0;
  width: 95%;
  max-width: 1400px;
  max-height: 98vh;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  transform: translateY(-20px);
  transition: transform 0.3s ease;
  overflow: hidden;
}

.modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  padding: 20px 25px;
  background-color: var(--primary-color);
  border-bottom: none;
  position: relative;
  color: white;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-header h2::before {
  content: "👤";
  font-size: 22px;
}

.close {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  font-weight: bold;
  color: white;
  cursor: pointer;
  transition: opacity 0.2s ease;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 25px;
  max-height: 85vh;
  overflow-y: auto;
  background-color: #ffffff;
}

.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

.modal-footer {
  padding: 20px 25px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  text-align: left;
}

/* تنسيقات النموذج البسيطة */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: border-color 0.3s ease;
  font-size: 14px;
  background-color: #ffffff;
  font-family: inherit;
}

.form-group input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
  outline: none;
}

/* تنسيقات أقسام النافذة المنبثقة */
.section-title {
  color: #333;
  font-size: 20px;
  margin: 25px 0 20px;
  padding-bottom: 12px;
  border-bottom: 3px solid var(--primary-color);
  font-weight: 700;
}

.user-info-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  border: 1px solid #e9ecef;
}

.user-info-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.user-info-section .section-title {
  margin: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.user-info-toggle-btn {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.user-info-toggle-btn:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.user-info-content {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  max-height: 500px;
  opacity: 1;
}

.user-info-content.hidden {
  max-height: 0;
  opacity: 0;
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
}

/* تصميم جديد كلياً لقسم الصلاحيات */
.permissions-container {
  background-color: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0;
  margin-bottom: 20px;
  max-height: 70vh;
  min-height: 60vh;
  overflow-y: auto;
}

.permissions-container::-webkit-scrollbar {
  width: 6px;
}

.permissions-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.permissions-container::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

/* تصميم أقسام الصلاحيات */
.permission-section {
  margin-bottom: 25px;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  background-color: #f8f9fa;
  padding: 18px 25px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background-color: #e9ecef;
}

.section-header h4 {
  margin: 0;
  color: #333;
  font-size: 17px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-all-btn {
  padding: 4px 8px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.toggle-all-btn:hover {
  background-color: var(--primary-hover);
}

.section-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  color: #666;
  transition: transform 0.3s ease;
}

.section-toggle .toggle-icon {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.section-toggle.collapsed .toggle-icon {
  transform: rotate(-90deg);
}

.section-content {
  padding: 25px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.section-content.collapsed {
  max-height: 0;
  padding: 0 25px;
  opacity: 0;
}

/* شبكة الصلاحيات */
.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  padding: 15px;
}

.permission-group {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
  margin-bottom: 15px;
}

.permission-group h5 {
  margin: 0 0 18px 0;
  color: #333;
  font-size: 15px;
  font-weight: 700;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--primary-color);
}

.permission-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 10px 15px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  min-height: 40px;
}

.permission-item:hover {
  background-color: #e9ecef;
}

.permission-item input[type="checkbox"] {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

.permission-item label {
  cursor: pointer;
  font-size: 14px;
  color: #333;
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.permission-item input[type="checkbox"]:checked + label {
  color: var(--primary-color);
  font-weight: 600;
}

.permission-item:has(input[type="checkbox"]:checked) {
  background-color: rgba(25, 118, 210, 0.1);
}

/* أزرار التحكم العامة */
.permissions-global-actions {
  padding: 15px 0;
  border-top: 1px solid #e9ecef;
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.global-action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.global-action-btn.primary {
  background-color: var(--primary-color);
  color: white;
}

.global-action-btn.primary:hover {
  background-color: var(--primary-hover);
}

.global-action-btn.secondary {
  background-color: #6c757d;
  color: white;
}

.global-action-btn.secondary:hover {
  background-color: #5a6268;
}

.global-action-btn:not(.primary):not(.secondary) {
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
}

.global-action-btn:not(.primary):not(.secondary):hover {
  background-color: #e9ecef;
}

.quick-action-btn {
  padding: 6px 12px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.quick-action-btn:hover {
  background-color: var(--primary-hover);
}

.quick-action-btn.secondary {
  background-color: #6c757d;
}

.quick-action-btn.secondary:hover {
  background-color: #5a6268;
}

/* تنسيقات أزرار النافذة المنبثقة البسيطة */
.confirm-btn, .cancel-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  min-width: 100px;
  text-align: center;
}

.confirm-btn {
  background-color: var(--primary-color);
  color: white;
}

.confirm-btn:hover {
  background-color: var(--primary-hover);
}

.cancel-btn {
  background-color: #6c757d;
  color: white;
  margin-right: 15px;
}

.cancel-btn:hover {
  background-color: #5a6268;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20px 25px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

/* عرض الصلاحيات في الجدول */
.permissions-badge {
  display: inline-block;
  background-color: #e0e0e0;
  color: #333;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin: 2px;
}

.permissions-count {
  font-weight: bold;
  cursor: pointer;
  color: #2196F3;
  text-decoration: underline;
}

/* تنسيقات الرسائل */
.message {
  padding: 10px 15px;
  margin: 10px 0;
  border-radius: 4px;
  font-size: 14px;
}

.success {
  background-color: #dff0d8;
  color: #3c763d;
  border: 1px solid #d6e9c6;
}

.error {
  background-color: #f2dede;
  color: #a94442;
  border: 1px solid #ebccd1;
}

/* تنسيقات الإجراءات السريعة */
.quick-actions {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.quick-actions h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.quick-btn {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.quick-btn:hover {
  background-color: #5a6268;
}

.quick-btn:last-child {
  margin-bottom: 0;
}

/* تحسينات بسيطة للتصميم */
/* تم حذف تنسيقات main-content - يتم التحكم بها من sidebar.css فقط */

.users-page h1 {
  color: #333;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 25px;
}

/* تحسين عرض الصلاحيات في الجدول */
.permissions-badge {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  margin: 1px;
}

.permissions-count {
  font-weight: 600;
  cursor: pointer;
  color: var(--primary-color);
  text-decoration: underline;
}

/* تنسيقات متجاوبة محسنة */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 2% auto;
    border-radius: 16px;
  }

  .permissions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  /* تم حذف تنسيقات main-content - يتم التحكم بها من sidebar.css فقط */

  .users-page h1 {
    font-size: 24px;
    flex-direction: column;
    gap: 10px;
  }

  .modal-header {
    padding: 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-footer {
    padding: 20px;
    flex-direction: column;
    gap: 12px;
  }

  .confirm-btn, .cancel-btn {
    width: 100%;
    margin-right: 0;
  }

  .users-table th,
  .users-table td {
    padding: 12px 8px;
    font-size: 14px;
  }

  .action-btn {
    margin: 0 3px;
    padding: 6px 8px;
    font-size: 12px;
    min-width: 35px;
    height: 32px;
  }

  .add-user-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    margin: 1% auto;
  }

  .permissions-section {
    padding: 15px;
  }

  .permission-item {
    padding: 10px 12px;
  }

  .form-group input {
    padding: 12px 14px;
  }

  .users-table-container {
    padding: 15px;
  }
}

/* تنسيقات إضافية بسيطة */
.users-table td {
  line-height: 1.4;
}

.users-table .username-cell {
  font-weight: 600;
  color: var(--primary-color);
}